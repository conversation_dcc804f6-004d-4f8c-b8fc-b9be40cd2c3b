# Solar Prediction System - Docker Installation

This package contains the complete Solar Prediction System as a Docker container.

## Prerequisites

1. **Docker Desktop** - Download and install from: https://docs.docker.com/desktop/windows/
2. **Windows 10 version 1903+** (for built-in tar support) OR **7-Zip** (https://www.7-zip.org/)
3. **At least 4GB RAM** available for the container
4. **At least 10GB free disk space**

## Installation Options

### Option 1: Simple Batch Script (Recommended)
```cmd
install_solar_simple.bat
```
- Uses built-in Windows tar command
- Works on Windows 10 1903+ and Windows 11
- No additional software required

### Option 2: Full Batch Script
```cmd
install_solar_docker.bat
```
- Supports both tar and 7-Zip
- More comprehensive error checking
- Works on older Windows versions with 7-Zip

### Option 3: PowerShell Script
```powershell
.\install_solar_simple.ps1
```
- Modern PowerShell interface
- Better error handling and progress display
- Requires PowerShell 5.0+

## Quick Start

1. **Download** the `solar-prediction-complete.tar.gz` file to this folder
2. **Run** one of the installation scripts:
   - Double-click `install_solar_simple.bat`, OR
   - Open Command Prompt and run `install_solar_docker.bat`, OR
   - Open PowerShell and run `.\install_solar_simple.ps1`
3. **Wait** for the installation to complete (5-10 minutes)
4. **Access** the system at http://localhost:8100

## Services and Ports

After installation, the following services will be available:

- **Web Interface**: http://localhost:8100
- **API Documentation**: http://localhost:8100/docs
- **PostgreSQL Database**: localhost:5432 (username: postgres, password: postgres)
- **Telegram Bot**: Port 8109
- **Enhanced Billing Service**: Port 8110

## Management Commands

### Check Status
```cmd
docker ps
```

### View Logs
```cmd
docker logs solar-prediction-system
```

### Stop System
```cmd
docker stop solar-prediction-system
```

### Start System
```cmd
docker start solar-prediction-system
```

### Remove System
```cmd
docker stop solar-prediction-system
docker rm solar-prediction-system
```

### Update System
1. Stop and remove the old container:
   ```cmd
   docker stop solar-prediction-system
   docker rm solar-prediction-system
   ```
2. Remove the old image:
   ```cmd
   docker rmi solar-prediction-complete:latest
   ```
3. Run the installation script again with the new image file

## Troubleshooting

### Installation Issues

**Error: Docker not found**
- Install Docker Desktop from https://docs.docker.com/desktop/windows/
- Make sure Docker Desktop is running
- Restart your command prompt/PowerShell

**Error: tar command not found**
- Use `install_solar_docker.bat` instead
- Install 7-Zip from https://www.7-zip.org/

**Error: Port already in use**
- Check what's using the ports: `netstat -an | findstr ":8100"`
- Stop conflicting services or change ports in the Docker run command

### Runtime Issues

**Container won't start**
- Check logs: `docker logs solar-prediction-system`
- Ensure you have enough memory (4GB+)
- Try restarting: `docker restart solar-prediction-system`

**Web interface not accessible**
- Wait 2-3 minutes after container start
- Check if container is running: `docker ps`
- Try accessing http://127.0.0.1:8100 instead

**Database connection issues**
- The database takes 1-2 minutes to initialize on first run
- Check container logs for database startup messages
- Ensure port 5432 is not blocked by firewall

## System Requirements

- **OS**: Windows 10 version 1903+ or Windows 11
- **RAM**: 4GB minimum, 8GB recommended
- **Disk**: 10GB free space minimum
- **Network**: Internet connection for initial setup

## File Structure

```
solar/
├── install_solar_simple.bat      # Simple installation (Windows 10+)
├── install_solar_docker.bat      # Full installation (all Windows)
├── install_solar_simple.ps1      # PowerShell installation
├── install_solar_docker.ps1      # Advanced PowerShell installation
├── solar-prediction-complete.tar.gz  # Docker image (download separately)
└── README.md                     # This file
```

## Support

If you encounter issues:

1. Check the logs: `docker logs solar-prediction-system`
2. Verify Docker is running: `docker info`
3. Ensure all ports are free: `netstat -an | findstr ":8100\|:5432\|:8109\|:8110"`
4. Try restarting the container: `docker restart solar-prediction-system`

For persistent issues, provide the output of:
- `docker logs solar-prediction-system`
- `docker ps -a`
- `docker images`
