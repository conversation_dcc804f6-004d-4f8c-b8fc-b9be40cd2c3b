@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Simple Windows Docker Installation
:: This version uses only built-in Windows tools (tar)

echo.
echo Solar Prediction System - Simple Installation
echo ============================================
echo.

:: Check Docker
echo Checking Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not running
    echo.
    echo Please:
    echo 1. Install Docker Desktop from: https://docs.docker.com/desktop/windows/
    echo 2. Make sure Docker Desktop is running
    echo 3. Restart Command Prompt
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Docker found

:: Check for tar (Windows 10 1903+)
where tar >nul 2>&1
if errorlevel 1 (
    echo ERROR: tar command not found
    echo This script requires Windows 10 version 1903 or later
    echo.
    echo Alternative: Use install_solar_docker.bat with 7-Zip installed
    echo.
    pause
    exit /b 1
)

echo SUCCESS: tar command available

:: Check image file
set IMAGE_FILE=solar-prediction-complete.tar.gz
if not exist "%IMAGE_FILE%" (
    echo ERROR: File '%IMAGE_FILE%' not found
    echo Please make sure the file is in the same folder
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Image file found: %IMAGE_FILE%

:: Extract and load directly
echo.
echo Extracting and loading Docker image...
echo This may take a few minutes...

:: Extract .tar.gz and load in one step
tar -xzf "%IMAGE_FILE%" --to-stdout | docker load

if errorlevel 1 (
    echo ERROR: Failed to extract and load Docker image
    echo.
    echo Trying alternative method...
    echo Extracting to temporary file...
    tar -xzf "%IMAGE_FILE%"
    if exist "solar-prediction-complete.tar" (
        echo Loading Docker image...
        docker load -i "solar-prediction-complete.tar"
        if errorlevel 1 (
            echo ERROR: Failed to load Docker image
            pause
            exit /b 1
        )
        del "solar-prediction-complete.tar" >nul 2>&1
    ) else (
        echo ERROR: Failed to extract tar file
        pause
        exit /b 1
    )
)

echo SUCCESS: Docker image loaded

:: Check if container exists
docker ps -a --filter "name=solar-prediction-system" --format "{{.Names}}" | findstr "solar-prediction-system" >nul
if not errorlevel 1 (
    echo.
    echo WARNING: Container 'solar-prediction-system' already exists
    set /p "REMOVE=Remove existing container? (y/N): "
    if /i "!REMOVE!"=="y" (
        echo Removing existing container...
        docker stop solar-prediction-system >nul 2>&1
        docker rm solar-prediction-system >nul 2>&1
    ) else (
        echo Installation cancelled
        pause
        exit /b 1
    )
)

:: Start container
echo.
echo Starting Solar Prediction System...
docker run -d ^
    --name solar-prediction-system ^
    --restart unless-stopped ^
    -p 5432:5432 ^
    -p 8100:8100 ^
    -p 8109:8109 ^
    -p 8110:8110 ^
    solar-prediction-complete:latest

if errorlevel 1 (
    echo ERROR: Failed to start container
    echo.
    echo Container logs:
    docker logs solar-prediction-system 2>nul
    pause
    exit /b 1
)

:: Wait and check
echo Waiting for services to start (30 seconds)...
timeout /t 30 /nobreak >nul

docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" | findstr /i "Up" >nul
if errorlevel 1 (
    echo ERROR: Container failed to start properly
    echo.
    echo Container logs:
    docker logs solar-prediction-system
    echo.
    echo Troubleshooting:
    echo - Check if ports 5432, 8100, 8109, 8110 are free
    echo - Ensure you have enough memory (4GB+)
    echo - Try: docker restart solar-prediction-system
    pause
    exit /b 1
)

:: Success
echo.
echo SUCCESS: Solar Prediction System is running!
echo.
echo Available services:
echo - Web Interface: http://localhost:8100
echo - API Documentation: http://localhost:8100/docs
echo - PostgreSQL: localhost:5432 (postgres/postgres)
echo - Telegram Bot: Port 8109
echo - Enhanced Billing: Port 8110
echo.
echo Management commands:
echo - Check status: docker ps
echo - View logs: docker logs solar-prediction-system
echo - Stop system: docker stop solar-prediction-system
echo - Start system: docker start solar-prediction-system
echo - Remove system: docker rm -f solar-prediction-system
echo.

set /p "OPEN_WEB=Open web interface now? (y/N): "
if /i "!OPEN_WEB!"=="y" (
    start http://localhost:8100
)

echo.
echo Installation completed!
pause
