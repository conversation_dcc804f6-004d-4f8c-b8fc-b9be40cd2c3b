{"architecture": "amd64", "config": {"Hostname": "", "Domainname": "", "User": "root", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "ExposedPorts": {"5432/tcp": {}, "8100/tcp": {}, "8109/tcp": {}, "8110/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "LANG=C.UTF-8", "GPG_KEY=A035C8C19219BA821ECEA86B64E628F8D684696D", "PYTHON_VERSION=3.11.13", "PYTHON_SHA256=8fb5f9fbc7609fa822cb31549884575db7fd9657cbffb89510b5d7975963a83a", "DEBIAN_FRONTEND=noninteractive", "PYTHONUNBUFFERED=1", "PYTHONDONTWRITEBYTECODE=1", "PYTHONPATH=/app:/app/src", "PYTHONRECURSIONLIMIT=10000", "TZ=Europe/Athens", "PGDATA=/var/lib/postgresql/data", "POSTGRES_DB=solar_prediction", "POSTGRES_USER=postgres", "POSTGRES_PASSWORD=postgres"], "Cmd": ["/app/start_complete_system.sh"], "Healthcheck": {"Test": ["CMD-SHELL", "curl -f http://localhost:8100/health || exit 1"], "Interval": 30000000000, "Timeout": 10000000000, "StartPeriod": 60000000000, "Retries": 3}, "Image": "sha256:c9a93b7b029cc70596e9eedef6d322114888b68b0ebc6b0aacc8e8d2fedd3e72", "Volumes": null, "WorkingDir": "/app", "Entrypoint": null, "OnBuild": null, "Labels": null}, "container": "906dae7ded02d713bb461ca3c2a44eabbd61210f23e457e8748fa2ca7b27d37a", "container_config": {"Hostname": "906dae7ded02", "Domainname": "", "User": "root", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "ExposedPorts": {"5432/tcp": {}, "8100/tcp": {}, "8109/tcp": {}, "8110/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "LANG=C.UTF-8", "GPG_KEY=A035C8C19219BA821ECEA86B64E628F8D684696D", "PYTHON_VERSION=3.11.13", "PYTHON_SHA256=8fb5f9fbc7609fa822cb31549884575db7fd9657cbffb89510b5d7975963a83a", "DEBIAN_FRONTEND=noninteractive", "PYTHONUNBUFFERED=1", "PYTHONDONTWRITEBYTECODE=1", "PYTHONPATH=/app:/app/src", "PYTHONRECURSIONLIMIT=10000", "TZ=Europe/Athens", "PGDATA=/var/lib/postgresql/data", "POSTGRES_DB=solar_prediction", "POSTGRES_USER=postgres", "POSTGRES_PASSWORD=postgres"], "Cmd": ["/bin/sh", "-c", "#(nop) ", "CMD [\"/app/start_complete_system.sh\"]"], "Healthcheck": {"Test": ["CMD-SHELL", "curl -f http://localhost:8100/health || exit 1"], "Interval": 30000000000, "Timeout": 10000000000, "StartPeriod": 60000000000, "Retries": 3}, "Image": "sha256:c9a93b7b029cc70596e9eedef6d322114888b68b0ebc6b0aacc8e8d2fedd3e72", "Volumes": null, "WorkingDir": "/app", "Entrypoint": null, "OnBuild": null, "Labels": {}}, "created": "2025-06-28T20:03:21.38770158Z", "docker_version": "27.5.1", "history": [{"created": "2025-06-03T23:02:53Z", "created_by": "# debian.sh --arch 'amd64' out/ 'bookworm' '@**********'", "comment": "debuerreotype 0.15"}, {"created": "2025-06-03T23:02:53Z", "created_by": "ENV PATH=/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-03T23:02:53Z", "created_by": "ENV LANG=C.UTF-8", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-03T23:02:53Z", "created_by": "RUN /bin/sh -c set -eux; \tapt-get update; \tapt-get install -y --no-install-recommends \t\tca-certificates \t\tnetbase \t\ttzdata \t; \trm -rf /var/lib/apt/lists/* # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-03T23:02:53Z", "created_by": "ENV GPG_KEY=A035C8C19219BA821ECEA86B64E628F8D684696D", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-03T23:02:53Z", "created_by": "ENV PYTHON_VERSION=3.11.13", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-03T23:02:53Z", "created_by": "ENV PYTHON_SHA256=8fb5f9fbc7609fa822cb31549884575db7fd9657cbffb89510b5d7975963a83a", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-03T23:02:53Z", "created_by": "RUN /bin/sh -c set -eux; \t\tsavedAptMark=\"$(apt-mark showmanual)\"; \tapt-get update; \tapt-get install -y --no-install-recommends \t\tdpkg-dev \t\tgcc \t\tgnupg \t\tlibbluetooth-dev \t\tlibbz2-dev \t\tlibc6-dev \t\tlibdb-dev \t\tlibffi-dev \t\tlibgdbm-dev \t\tliblzma-dev \t\tlibncursesw5-dev \t\tlibreadline-dev \t\tlibsqlite3-dev \t\tlibssl-dev \t\tmake \t\ttk-dev \t\tuuid-dev \t\twget \t\txz-utils \t\tzlib1g-dev \t; \t\twget -O python.tar.xz \"https://www.python.org/ftp/python/${PYTHON_VERSION%%[a-z]*}/Python-$PYTHON_VERSION.tar.xz\"; \techo \"$PYTHON_SHA256 *python.tar.xz\" | sha256sum -c -; \twget -O python.tar.xz.asc \"https://www.python.org/ftp/python/${PYTHON_VERSION%%[a-z]*}/Python-$PYTHON_VERSION.tar.xz.asc\"; \tGNUPGHOME=\"$(mktemp -d)\"; export GNUPGHOME; \tgpg --batch --keyserver hkps://keys.openpgp.org --recv-keys \"$GPG_KEY\"; \tgpg --batch --verify python.tar.xz.asc python.tar.xz; \tgpgconf --kill all; \trm -rf \"$GNUPGHOME\" python.tar.xz.asc; \tmkdir -p /usr/src/python; \ttar --extract --directory /usr/src/python --strip-components=1 --file python.tar.xz; \trm python.tar.xz; \t\tcd /usr/src/python; \tgnuArch=\"$(dpkg-architecture --query DEB_BUILD_GNU_TYPE)\"; \t./configure \t\t--build=\"$gnuArch\" \t\t--enable-loadable-sqlite-extensions \t\t--enable-optimizations \t\t--enable-option-checking=fatal \t\t--enable-shared \t\t$(test \"$gnuArch\" != 'riscv64-linux-musl' && echo '--with-lto') \t\t--with-ensurepip \t; \tnproc=\"$(nproc)\"; \tEXTRA_CFLAGS=\"$(dpkg-buildflags --get CFLAGS)\"; \tLDFLAGS=\"$(dpkg-buildflags --get LDFLAGS)\"; \tLDFLAGS=\"${LDFLAGS:--Wl},--strip-all\"; \tmake -j \"$nproc\" \t\t\"EXTRA_CFLAGS=${EXTRA_CFLAGS:-}\" \t\t\"LDFLAGS=${LDFLAGS:-}\" \t; \trm python; \tmake -j \"$nproc\" \t\t\"EXTRA_CFLAGS=${EXTRA_CFLAGS:-}\" \t\t\"LDFLAGS=${LDFLAGS:--Wl},-rpath='\\$\\$ORIGIN/../lib'\" \t\tpython \t; \tmake install; \t\tcd /; \trm -rf /usr/src/python; \t\tfind /usr/local -depth \t\t\\( \t\t\t\\( -type d -a \\( -name test -o -name tests -o -name idle_test \\) \\) \t\t\t-o \\( -type f -a \\( -name '*.pyc' -o -name '*.pyo' -o -name 'libpython*.a' \\) \\) \t\t\\) -exec rm -rf '{}' + \t; \t\tldconfig; \t\tapt-mark auto '.*' > /dev/null; \tapt-mark manual $savedAptMark; \tfind /usr/local -type f -executable -not \\( -name '*tkinter*' \\) -exec ldd '{}' ';' \t\t| awk '/=>/ { so = $(NF-1); if (index(so, \"/usr/local/\") == 1) { next }; gsub(\"^/(usr/)?\", \"\", so); printf \"*%s\\n\", so }' \t\t| sort -u \t\t| xargs -r dpkg-query --search \t\t| cut -d: -f1 \t\t| sort -u \t\t| xargs -r apt-mark manual \t; \tapt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false; \trm -rf /var/lib/apt/lists/*; \t\texport PYTHONDONTWRITEBYTECODE=1; \tpython3 --version; \t\tpip3 install \t\t--disable-pip-version-check \t\t--no-cache-dir \t\t--no-compile \t\t'setuptools==65.5.1' \t\t'wheel<0.46' \t; \tpip3 --version # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-03T23:02:53Z", "created_by": "RUN /bin/sh -c set -eux; \tfor src in idle3 pip3 pydoc3 python3 python3-config; do \t\tdst=\"$(echo \"$src\" | tr -d 3)\"; \t\t[ -s \"/usr/local/bin/$src\" ]; \t\t[ ! -e \"/usr/local/bin/$dst\" ]; \t\tln -svT \"$src\" \"/usr/local/bin/$dst\"; \tdone # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-03T23:02:53Z", "created_by": "CMD [\"python3\"]", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-28T19:07:15.244828589Z", "created_by": "/bin/sh -c #(nop)  ENV DEBIAN_FRONTEND=noninteractive PYTHONUNBUFFERED=1 PYTHONDONTWRITEBYTECODE=1 PYTHONPATH=/app:/app/src PYTHONRECURSIONLIMIT=10000 TZ=Europe/Athens PGDATA=/var/lib/postgresql/data POSTGRES_DB=solar_prediction POSTGRES_USER=postgres POSTGRES_PASSWORD=postgres", "empty_layer": true}, {"created": "2025-06-28T19:08:06.491472234Z", "created_by": "/bin/sh -c apt-get update && apt-get install -y     postgresql     postgresql-client     postgresql-contrib     curl     wget     git     supervisor     netcat-openbsd     gcc     g++     make     pkg-config     libpq-dev     libgomp1     tzdata     && rm -rf /var/lib/apt/lists/*     && apt-get clean"}, {"created": "2025-06-28T19:08:09.866886331Z", "created_by": "/bin/sh -c ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone"}, {"created": "2025-06-28T19:08:10.408520161Z", "created_by": "/bin/sh -c groupadd -r solarapp && useradd -r -g solarapp solarapp"}, {"created": "2025-06-28T19:08:10.507827231Z", "created_by": "/bin/sh -c #(nop) WORKDIR /app"}, {"created": "2025-06-28T19:08:13.286769777Z", "created_by": "/bin/sh -c mkdir -p     /app/logs     /app/models     /app/data     /app/static     /app/templates     /app/scripts     /app/docs     /app/sql     /app/src     /app/backup_20250615_164048     /var/lib/postgresql/data     /var/log/postgresql     /var/run/postgresql     && chown -R postgres:postgres /var/lib/postgresql     && chown -R postgres:postgres /var/log/postgresql     && chown -R postgres:postgres /var/run/postgresql     && chown -R solarapp:solarapp /app"}, {"created": "2025-06-28T19:55:42.495424931Z", "created_by": "/bin/sh -c #(nop) COPY --chown=solarapp:solarapp dir:53b644cd8786333f8da68113d2cefd3f279567b988dac4f2816eb8538aa10a8c in /app/ "}, {"created": "2025-06-28T20:02:30.200870247Z", "created_by": "/bin/sh -c pip install --no-cache-dir --upgrade pip &&     if [ -f /app/requirements.txt ]; then         pip install --no-cache-dir -r /app/requirements.txt;     elif [ -f /app/requirements.docker.txt ]; then         pip install --no-cache-dir -r /app/requirements.docker.txt;     fi"}, {"created": "2025-06-28T20:02:41.318193935Z", "created_by": "/bin/sh -c #(nop)  USER postgres", "empty_layer": true}, {"created": "2025-06-28T20:02:44.80843204Z", "created_by": "/bin/sh -c /usr/lib/postgresql/15/bin/initdb -D /var/lib/postgresql/data --encoding=UTF8 --lc-collate=C --lc-ctype=C"}, {"created": "2025-06-28T20:02:45.53748686Z", "created_by": "/bin/sh -c echo \"host all all 0.0.0.0/0 md5\" >> /var/lib/postgresql/data/pg_hba.conf &&     echo \"listen_addresses='*'\" >> /var/lib/postgresql/data/postgresql.conf &&     echo \"port = 5432\" >> /var/lib/postgresql/data/postgresql.conf &&     echo \"max_connections = 100\" >> /var/lib/postgresql/data/postgresql.conf &&     echo \"shared_buffers = 128MB\" >> /var/lib/postgresql/data/postgresql.conf"}, {"created": "2025-06-28T20:03:13.308924345Z", "created_by": "/bin/sh -c /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data start &&     sleep 5 &&     createdb solar_prediction &&     echo \"Creating current backup from host database...\" &&     PGPASSWORD=postgres pg_dump -h host.docker.internal -p 5433 -U postgres -d solar_prediction > /tmp/current_database_backup_20250628_225402.sql 2>/dev/null ||     echo \"Could not connect to host database, using fallback backup...\" &&     if [ -f /tmp/current_database_backup_20250628_225402.sql ] && [ -s /tmp/current_database_backup_20250628_225402.sql ]; then         echo \"Loading current database backup...\" &&         psql -d solar_prediction -f /tmp/current_database_backup_20250628_225402.sql;     elif [ -f /app/current_database_backup_20250628_225402.sql ]; then         echo \"Loading current database backup...\" &&         psql -d solar_prediction -f /app/current_database_backup_20250628_225402.sql;     elif [ -f /app/backup_20250615_164048/complete_database_backup.sql ]; then         echo \"Loading fallback database backup...\" &&         psql -d solar_prediction -f /app/backup_20250615_164048/complete_database_backup.sql;     elif [ -f /app/scripts/final_backup_20250616_011741.sql ]; then         echo \"Loading legacy database backup...\" &&         psql -d solar_prediction -f /app/scripts/final_backup_20250616_011741.sql;     else         echo \"No database backup found!\";     fi &&     rm -f /tmp/current_database_backup_20250628_225402.sql &&     /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data stop"}, {"created": "2025-06-28T20:03:19.855056611Z", "created_by": "/bin/sh -c #(nop)  USER root", "empty_layer": true}, {"created": "2025-06-28T20:03:20.308851109Z", "created_by": "/bin/sh -c mkdir -p /etc/supervisor/conf.d &&     echo '[supervisord]' > /etc/supervisor/conf.d/supervisord.conf &&     echo 'nodaemon=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'user=root' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'logfile=/var/log/supervisor/supervisord.log' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'pidfile=/var/run/supervisord.pid' >> /etc/supervisor/conf.d/supervisord.conf &&     echo '' >> /etc/supervisor/conf.d/supervisord.conf &&     echo '[program:postgresql]' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'command=/usr/lib/postgresql/15/bin/postgres -D /var/lib/postgresql/data' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'user=postgres' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'autostart=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'autorestart=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'stdout_logfile=/var/log/postgresql/postgresql.log' >> /etc/supervisor/conf.d/supervisord.conf &&     echo '' >> /etc/supervisor/conf.d/supervisord.conf &&     echo '[program:solar-prediction-app]' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'command=python /app/scripts/production_app.py' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'directory=/app' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'user=solarapp' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'autostart=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'autorestart=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'stdout_logfile=/app/logs/production_app.log' >> /etc/supervisor/conf.d/supervisord.conf &&     echo 'environment=DATABASE_URL=\"postgresql://postgres:postgres@localhost:5432/solar_prediction\",PYTHONPATH=\"/app:/app/src\"' >> /etc/supervisor/conf.d/supervisord.conf"}, {"created": "2025-06-28T20:03:20.704740514Z", "created_by": "/bin/sh -c echo '#!/bin/bash' > /app/start_complete_system.sh &&     echo 'set -e' >> /app/start_complete_system.sh &&     echo '' >> /app/start_complete_system.sh &&     echo 'echo \"🚀 Starting Solar Prediction Complete System...\"' >> /app/start_complete_system.sh &&     echo '' >> /app/start_complete_system.sh &&     echo '# Ensure PostgreSQL data directory permissions' >> /app/start_complete_system.sh &&     echo 'chown -R postgres:postgres /var/lib/postgresql/data' >> /app/start_complete_system.sh &&     echo 'chmod 700 /var/lib/postgresql/data' >> /app/start_complete_system.sh &&     echo '' >> /app/start_complete_system.sh &&     echo '# Ensure application permissions' >> /app/start_complete_system.sh &&     echo 'chown -R solarapp:solarapp /app/logs' >> /app/start_complete_system.sh &&     echo 'chown -R solarapp:solarapp /app/models' >> /app/start_complete_system.sh &&     echo 'chown -R solarapp:solarapp /app/data' >> /app/start_complete_system.sh &&     echo '' >> /app/start_complete_system.sh &&     echo '# Start supervisor (which starts PostgreSQL and the app)' >> /app/start_complete_system.sh &&     echo 'echo \"📊 Starting services with supervisor...\"' >> /app/start_complete_system.sh &&     echo 'exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf' >> /app/start_complete_system.sh"}, {"created": "2025-06-28T20:03:21.13370153Z", "created_by": "/bin/sh -c chmod +x /app/start_complete_system.sh"}, {"created": "2025-06-28T20:03:21.227112536Z", "created_by": "/bin/sh -c #(nop)  EXPOSE 5432 8100 8109 8110", "empty_layer": true}, {"created": "2025-06-28T20:03:21.319650095Z", "created_by": "/bin/sh -c #(nop)  HEALTHCHECK &{[\"CMD-SHELL\" \"curl -f http://localhost:8100/health || exit 1\"] \"30s\" \"10s\" \"1m0s\" \"0s\" '\\x03'}", "empty_layer": true}, {"created": "2025-06-28T20:03:21.38770158Z", "created_by": "/bin/sh -c #(nop)  CMD [\"/app/start_complete_system.sh\"]", "empty_layer": true}], "os": "linux", "rootfs": {"type": "layers", "diff_ids": ["sha256:7fb72a7d1a8e984ccd01277432de660162a547a00de77151518dc9033cfb8cb4", "sha256:905dadf3a0ed88106f48ce02feca63f9b8cbdaf38713c0f090fdff68bc849708", "sha256:d731454f914a582c05b53a9c0ff24018474965a8b77c231c376600b14c9a16af", "sha256:79d7628d5b6b9722f4d4e8a2c9b5f33b778379018a6a24d74846194ca0b41ad1", "sha256:c9fca4d52c77cba43c4153ae70edb9f86e2d84a915e08465ae39e228c0845555", "sha256:67559425bffbacf81649679a61ad0c184dafe931112dac8313f8c57bffa7922b", "sha256:2fcbbb33f89b6ce8a72791037bb6a1b4eb4b5398c711cc7fb8e8e9298735439f", "sha256:fca9093361c6e4a5e07d93c44f178e85e579c106a8c3cac51c893e7531fed1b5", "sha256:c0f1cda2f123ede6060b6af0a61297ae4e9ee8deb6e28b47c966c8753f23c771", "sha256:415523251e9fadee2a1f3ffdf7a101a132821a896dcb2869567b191e51bc6c7b", "sha256:285ec9e4f27d6a4c348cbf0449d139b59007c22a5409ff6ddfb2518c201bcc93", "sha256:d1b420bd40e7127424cecfafb4c423a067961569a8cc1c1f2e349431bf041718", "sha256:050bededc4ac084bf1f3ba50b90a5ad1363863e842f46b0385190ed9cd782176", "sha256:f16ef1ca21a0f91f2d39144ae577e696ce7a4159acd71a0f00b815e3272f26d5", "sha256:1928ef82004222d94e41f4da26ce298fefb6b65ce7719c1a64ceee9aea42e869", "sha256:daa544ee5b9128dc07cdcca536bfc15d9eed845d1d9eb61dd27dd7f61765f764", "sha256:46c1aac63a3b793fe623797bf3fef92d054e54c524aa04404ce74d0686923cef"]}}