# Solar Prediction System - Simple PowerShell Installation Script
# Usage: .\install_solar_simple.ps1

param(
    [switch]$Force,
    [switch]$NoWait
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "Solar Prediction System - Simple Installation" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Check Docker
Write-Host "Checking Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker command failed"
    }
    Write-Host "SUCCESS: Docker found - $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker is not installed or not running" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please:" -ForegroundColor Yellow
    Write-Host "1. Install Docker Desktop from: https://docs.docker.com/desktop/windows/"
    Write-Host "2. Make sure Docker Desktop is running"
    Write-Host "3. Restart PowerShell"
    Write-Host ""
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

# Check Docker daemon
try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker daemon not running"
    }
    Write-Host "SUCCESS: Docker daemon is running" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker daemon is not running" -ForegroundColor Red
    Write-Host "Please start Docker Desktop" -ForegroundColor Yellow
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

# Check image file
$imageFile = "solar-prediction-complete.tar.gz"
if (-not (Test-Path $imageFile)) {
    Write-Host "ERROR: File '$imageFile' not found" -ForegroundColor Red
    Write-Host "Please make sure the file is in the same folder as this script" -ForegroundColor Yellow
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

Write-Host "SUCCESS: Image file found: $imageFile" -ForegroundColor Green

# Extract and load image
Write-Host ""
Write-Host "Extracting and loading Docker image..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Yellow

try {
    # Use traditional method - extract then load
    Write-Host "Extracting archive..." -ForegroundColor Yellow
    tar -xzf $imageFile
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to extract archive"
    }
    
    $tarFile = "solar-prediction-complete.tar"
    if (-not (Test-Path $tarFile)) {
        throw "Extracted tar file not found"
    }
    
    Write-Host "Loading Docker image..." -ForegroundColor Yellow
    docker load -i $tarFile
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to load Docker image"
    }
    
    # Cleanup
    Write-Host "Cleaning up..." -ForegroundColor Yellow
    Remove-Item $tarFile -ErrorAction SilentlyContinue
    
    Write-Host "SUCCESS: Docker image loaded" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to extract and load Docker image" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

# Check if container exists
$existingContainer = docker ps -a --filter "name=solar-prediction-system" --format "{{.Names}}" 2>$null
if ($existingContainer -eq "solar-prediction-system") {
    Write-Host ""
    Write-Host "WARNING: Container 'solar-prediction-system' already exists" -ForegroundColor Yellow
    
    if (-not $Force) {
        $response = Read-Host "Remove existing container and create new one? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "Installation cancelled" -ForegroundColor Yellow
            if (-not $NoWait) { Read-Host "Press Enter to exit" }
            exit 1
        }
    }
    
    Write-Host "Removing existing container..." -ForegroundColor Yellow
    docker stop solar-prediction-system 2>$null | Out-Null
    docker rm solar-prediction-system 2>$null | Out-Null
}

# Start container
Write-Host ""
Write-Host "Starting Solar Prediction System..." -ForegroundColor Yellow

$dockerArgs = @(
    "run", "-d",
    "--name", "solar-prediction-system",
    "--restart", "unless-stopped",
    "-p", "5432:5432",
    "-p", "8100:8100", 
    "-p", "8109:8109",
    "-p", "8110:8110",
    "solar-prediction-complete:latest"
)

try {
    docker @dockerArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Docker run command failed"
    }
} catch {
    Write-Host "ERROR: Failed to start container" -ForegroundColor Red
    Write-Host ""
    Write-Host "Container logs:" -ForegroundColor Yellow
    docker logs solar-prediction-system 2>$null
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

# Wait for startup
Write-Host "Waiting for services to start (30 seconds)..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check container status
$containerStatus = docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" 2>$null
if ($containerStatus -notmatch "Up") {
    Write-Host "ERROR: Container failed to start properly" -ForegroundColor Red
    Write-Host ""
    Write-Host "Container logs:" -ForegroundColor Yellow
    docker logs solar-prediction-system
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "- Check if ports 5432, 8100, 8109, 8110 are free"
    Write-Host "- Ensure you have enough memory (4GB+)"
    Write-Host "- Try: docker restart solar-prediction-system"
    if (-not $NoWait) { Read-Host "Press Enter to exit" }
    exit 1
}

# Success message
Write-Host ""
Write-Host "SUCCESS: Solar Prediction System is running!" -ForegroundColor Green
Write-Host ""
Write-Host "Available services:" -ForegroundColor Cyan
Write-Host "- Web Interface: http://localhost:8100" -ForegroundColor White
Write-Host "- API Documentation: http://localhost:8100/docs" -ForegroundColor White
Write-Host "- PostgreSQL: localhost:5432 (postgres/postgres)" -ForegroundColor White
Write-Host "- Telegram Bot: Port 8109" -ForegroundColor White
Write-Host "- Enhanced Billing: Port 8110" -ForegroundColor White
Write-Host ""
Write-Host "Management commands:" -ForegroundColor Cyan
Write-Host "- Check status: docker ps" -ForegroundColor White
Write-Host "- View logs: docker logs solar-prediction-system" -ForegroundColor White
Write-Host "- Stop system: docker stop solar-prediction-system" -ForegroundColor White
Write-Host "- Start system: docker start solar-prediction-system" -ForegroundColor White
Write-Host "- Remove system: docker rm -f solar-prediction-system" -ForegroundColor White
Write-Host ""

# Offer to open web interface
if (-not $NoWait) {
    $openWeb = Read-Host "Open web interface now? (y/N)"
    if ($openWeb -eq "y" -or $openWeb -eq "Y") {
        Start-Process "http://localhost:8100"
    }
}

Write-Host ""
Write-Host "Installation completed successfully!" -ForegroundColor Green

if (-not $NoWait) {
    Read-Host "Press Enter to exit"
}
