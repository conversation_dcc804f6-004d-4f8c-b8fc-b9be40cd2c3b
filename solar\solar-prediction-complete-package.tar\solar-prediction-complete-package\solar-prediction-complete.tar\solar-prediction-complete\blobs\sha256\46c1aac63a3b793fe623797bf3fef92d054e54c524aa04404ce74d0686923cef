app/                                                                                                0000755 0001747 0001747 00000000000 15030045210 010060  5                                                                                                    ustar 00                                                                0000000 0000000                                                                                                                                                                        app/start_complete_system.sh                                                                        0000755 0000000 0000000 00000001027 15030045210 015002  0                                                                                                    ustar 00                                                                0000000 0000000                                                                                                                                                                        #!/bin/bash
set -e

echo "🚀 Starting Solar Prediction Complete System..."

# Ensure PostgreSQL data directory permissions
chown -R postgres:postgres /var/lib/postgresql/data
chmod 700 /var/lib/postgresql/data

# Ensure application permissions
chown -R solarapp:solarapp /app/logs
chown -R solarapp:solarapp /app/models
chown -R solarapp:solarapp /app/data

# Start supervisor (which starts PostgreSQL and the app)
echo "📊 Starting services with supervisor..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         