# Solar Prediction System - Οδηγός Εγκατάστασης

## Περιεχόμενα Πακέτου

- `solar-prediction-complete.tar.gz` - Docker image (1.9GB)
- `install_solar_docker.sh` - Script αυτόματης εγκατάστασης (Linux/macOS)
- `install_solar_docker.ps1` - <PERSON>ript αυτόματης εγκατάστασης (Windows PowerShell)
- `install_solar_docker.bat` - Script αυτόματης εγκατάστασης (Windows Command Prompt)
- `INSTALLATION_GUIDE.md` - Αυτός ο οδηγός

## Απαιτήσεις Συστήματος

### Ελάχιστες Απαιτήσεις
- **RAM**: 4GB ελεύθερη μνήμη
- **Αποθηκευτικός χώρος**: 10GB ελεύθερος χώρος
- **Docker**: Έκδοση 20.10 ή νεότερη
- **Λειτουργικό σύστημα**: Linux, macOS, Windows με WSL2

### Συνιστώμενες Απαιτήσεις
- **RAM**: 8GB+ για βέλτιστη απόδοση
- **CPU**: 4+ cores
- **Αποθηκευτικός χώρος**: 20GB+ για logs και backups

## Εγκατάσταση Docker

### Ubuntu/Debian
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### CentOS/RHEL
```bash
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### Windows
```powershell
# Κατεβάστε το Docker Desktop από: https://docs.docker.com/desktop/windows/
# Ή χρησιμοποιήστε Chocolatey:
choco install docker-desktop

# Ή χρησιμοποιήστε winget:
winget install Docker.DockerDesktop
```

### macOS
Κατεβάστε το Docker Desktop από: https://docs.docker.com/desktop/mac/

## Εγκατάσταση Solar Prediction System

### Μέθοδος 1: Αυτόματη Εγκατάσταση (Συνιστάται)

#### Linux/macOS
```bash
# Κάντε το script εκτελέσιμο
chmod +x install_solar_docker.sh

# Εκτελέστε την εγκατάσταση
./install_solar_docker.sh
```

#### Windows PowerShell (Συνιστάται για Windows)
```powershell
# Εκτέλεση ως Administrator (προαιρετικό αλλά συνιστάται)
# Κλικ δεξί στο PowerShell -> "Run as Administrator"

# Εκτελέστε την εγκατάσταση
.\install_solar_docker.ps1

# Αν έχετε πρόβλημα με execution policy:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\install_solar_docker.ps1
```

#### Windows Command Prompt
```cmd
# Εκτελέστε την εγκατάσταση
install_solar_docker.bat
```

### Μέθοδος 2: Χειροκίνητη Εγκατάσταση

```bash
# 1. Αποσυμπίεση
gunzip solar-prediction-complete.tar.gz

# 2. Φόρτωση στο Docker
docker load -i solar-prediction-complete.tar

# 3. Εκκίνηση container
docker run -d \
  --name solar-prediction-system \
  --restart unless-stopped \
  -p 5432:5432 \
  -p 8100:8100 \
  -p 8109:8109 \
  -p 8110:8110 \
  solar-prediction-complete:latest

# 4. Έλεγχος κατάστασης
docker logs solar-prediction-system
```

## Πρόσβαση στις Υπηρεσίες

Μετά την επιτυχή εγκατάσταση:

- **🌐 Web Interface**: http://localhost:8100
- **📚 API Documentation**: http://localhost:8100/docs
- **🗄️ PostgreSQL**: localhost:5432
  - Username: `postgres`
  - Password: `postgres`
  - Database: `solar_prediction`
- **🤖 Telegram Bot**: Port 8109
- **💰 Enhanced Billing**: Port 8110

## Διαχείριση Container

### Βασικές Εντολές
```bash
# Κατάσταση
docker ps

# Logs
docker logs solar-prediction-system

# Διακοπή
docker stop solar-prediction-system

# Εκκίνηση
docker start solar-prediction-system

# Επανεκκίνηση
docker restart solar-prediction-system

# Αφαίρεση
docker stop solar-prediction-system
docker rm solar-prediction-system
```

### Πρόσβαση στο Container
```bash
# Bash shell
docker exec -it solar-prediction-system bash

# PostgreSQL
docker exec -it solar-prediction-system psql -U postgres -d solar_prediction

# Supervisor status
docker exec solar-prediction-system supervisorctl status
```

## Αντιμετώπιση Προβλημάτων

### Container δεν ξεκινάει
```bash
# Έλεγχος logs
docker logs solar-prediction-system

# Έλεγχος ports
netstat -tulpn | grep -E ':(5432|8100|8109|8110)'

# Έλεγχος Docker
docker info
```

### Πρόβλημα με ports
```bash
# Εύρεση διεργασιών που χρησιμοποιούν ports
sudo lsof -i :8100
sudo lsof -i :5432

# Εκκίνηση με διαφορετικά ports
docker run -d \
  --name solar-prediction-system \
  -p 5433:5432 \
  -p 8101:8100 \
  -p 8111:8109 \
  -p 8112:8110 \
  solar-prediction-complete:latest
```

### Πρόβλημα με μνήμη
```bash
# Έλεγχος χρήσης μνήμης
docker stats solar-prediction-system

# Αύξηση memory limit
docker run -d \
  --name solar-prediction-system \
  --memory="4g" \
  --restart unless-stopped \
  -p 5432:5432 -p 8100:8100 -p 8109:8109 -p 8110:8110 \
  solar-prediction-complete:latest
```

## Backup & Restore

### Backup Δεδομένων
```bash
# Backup βάσης δεδομένων
docker exec solar-prediction-system pg_dump -U postgres solar_prediction > backup_$(date +%Y%m%d).sql

# Backup ολόκληρου container
docker commit solar-prediction-system solar-prediction-backup:$(date +%Y%m%d)
```

### Restore Δεδομένων
```bash
# Restore από backup
docker exec -i solar-prediction-system psql -U postgres solar_prediction < backup_20250628.sql
```

## Ενημερώσεις

### Ενημέρωση Image
```bash
# Διακοπή τρέχοντος container
docker stop solar-prediction-system
docker rm solar-prediction-system

# Φόρτωση νέου image
docker load -i solar-prediction-complete-new.tar

# Εκκίνηση με νέο image
docker run -d --name solar-prediction-system \
  --restart unless-stopped \
  -p 5432:5432 -p 8100:8100 -p 8109:8109 -p 8110:8110 \
  solar-prediction-complete:latest
```

## Ασφάλεια

### Αλλαγή Passwords
```bash
# Πρόσβαση στο container
docker exec -it solar-prediction-system bash

# Αλλαγή PostgreSQL password
psql -U postgres -c "ALTER USER postgres PASSWORD 'new_password';"
```

### Firewall
```bash
# Ubuntu/Debian
sudo ufw allow 8100
sudo ufw allow 5432

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8100/tcp
sudo firewall-cmd --permanent --add-port=5432/tcp
sudo firewall-cmd --reload
```

## Υποστήριξη

Για τεχνική υποστήριξη:
1. Ελέγξτε τα logs: `docker logs solar-prediction-system`
2. Επιβεβαιώστε την κατάσταση: `docker ps`
3. Ελέγξτε τη συνδεσιμότητα: `curl http://localhost:8100`

---

**Έκδοση**: 1.0  
**Ημερομηνία**: 28 Ιουνίου 2025  
**Συμβατότητα**: Docker 20.10+
