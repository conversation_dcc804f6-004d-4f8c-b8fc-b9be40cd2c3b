@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Windows Docker Installation Script (Batch)
:: Χρήση: install_solar_docker.bat

echo.
echo 🚀 Solar Prediction System - Windows Docker Installation
echo ========================================================
echo.

:: Έλεγχος Docker
echo ✅ Έλεγχος Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Το Docker δεν είναι εγκατεστημένο ή δεν τρέχει
    echo.
    echo Παρακαλώ:
    echo 1. Εγκαταστήστε το Docker Desktop από: https://docs.docker.com/desktop/windows/
    echo 2. Βεβαιωθείτε ότι το Docker Desktop τρέχει
    echo 3. Επανεκκινήστε το Command Prompt
    echo.
    pause
    exit /b 1
)

:: Έλεγχος Docker daemon
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Το Docker daemon δεν τρέχει
    echo Παρακαλώ ξεκινήστε το Docker Desktop
    echo.
    pause
    exit /b 1
)

echo ✅ Docker βρέθηκε και τρέχει

:: Έλεγχος αρχείου image
set IMAGE_FILE=solar-prediction-complete.tar.gz
if not exist "%IMAGE_FILE%" (
    echo ❌ Το αρχείο '%IMAGE_FILE%' δεν βρέθηκε
    echo Παρακαλώ βεβαιωθείτε ότι το αρχείο βρίσκεται στον ίδιο φάκελο
    echo.
    pause
    exit /b 1
)

echo ✅ Αρχείο image βρέθηκε: %IMAGE_FILE%

:: Έλεγχος αν υπάρχει εργαλείο αποσυμπίεσης
set DECOMPRESS_TOOL=
where tar >nul 2>&1
if not errorlevel 1 (
    set DECOMPRESS_TOOL=tar
    goto :decompress_found
)

where 7z >nul 2>&1
if not errorlevel 1 (
    set DECOMPRESS_TOOL=7z
    goto :decompress_found
)

echo ❌ Δεν βρέθηκε εργαλείο αποσυμπίεσης
echo Παρακαλώ εγκαταστήστε:
echo - 7-Zip από: https://www.7-zip.org/
echo - Ή χρησιμοποιήστε Windows 10 1903+ που έχει ενσωματωμένο tar
echo.
pause
exit /b 1

:decompress_found
echo ✅ Εργαλείο αποσυμπίεσης: %DECOMPRESS_TOOL%

:: Αποσυμπίεση
echo.
echo 📦 Αποσυμπίεση αρχείου...
set TAR_FILE=solar-prediction-complete.tar

if "%DECOMPRESS_TOOL%"=="tar" (
    tar -xzf "%IMAGE_FILE%"
) else if "%DECOMPRESS_TOOL%"=="7z" (
    7z x "%IMAGE_FILE%" -y >nul
)

if errorlevel 1 (
    echo ❌ Σφάλμα κατά την αποσυμπίεση
    pause
    exit /b 1
)

if not exist "%TAR_FILE%" (
    echo ❌ Το αρχείο %TAR_FILE% δεν δημιουργήθηκε
    pause
    exit /b 1
)

echo ✅ Αποσυμπίεση ολοκληρώθηκε

:: Φόρτωση στο Docker
echo.
echo 🐳 Φόρτωση Docker image...
docker load -i "%TAR_FILE%"
if errorlevel 1 (
    echo ❌ Σφάλμα κατά τη φόρτωση του Docker image
    pause
    exit /b 1
)

echo ✅ Docker image φορτώθηκε επιτυχώς

:: Καθαρισμός
echo.
echo 🧹 Καθαρισμός προσωρινών αρχείων...
del "%TAR_FILE%" >nul 2>&1
echo ✅ Καθαρισμός ολοκληρώθηκε

:: Έλεγχος αν το container υπάρχει ήδη
for /f "tokens=*" %%i in ('docker ps -a --filter "name=solar-prediction-system" --format "{{.Names}}" 2^>nul') do (
    if "%%i"=="solar-prediction-system" (
        echo.
        echo ⚠️  Το container 'solar-prediction-system' υπάρχει ήδη
        set /p "REMOVE=Θέλετε να το αφαιρέσετε και να δημιουργήσετε νέο; (y/N): "
        if /i "!REMOVE!"=="y" (
            echo 🗑️  Αφαίρεση παλιού container...
            docker stop solar-prediction-system >nul 2>&1
            docker rm solar-prediction-system >nul 2>&1
        ) else (
            echo ❌ Εγκατάσταση ακυρώθηκε
            pause
            exit /b 1
        )
    )
)

:: Εκκίνηση container
echo.
echo 🚀 Εκκίνηση Solar Prediction System...
docker run -d ^
    --name solar-prediction-system ^
    --restart unless-stopped ^
    -p 5432:5432 ^
    -p 8100:8100 ^
    -p 8109:8109 ^
    -p 8110:8110 ^
    solar-prediction-complete:latest

if errorlevel 1 (
    echo ❌ Σφάλμα κατά την εκκίνηση του container
    echo Logs:
    docker logs solar-prediction-system 2>nul
    pause
    exit /b 1
)

:: Αναμονή για εκκίνηση
echo ⏳ Αναμονή για εκκίνηση υπηρεσιών (30 δευτερόλεπτα)...
timeout /t 30 /nobreak >nul

:: Έλεγχος κατάστασης
for /f "tokens=*" %%i in ('docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" 2^>nul') do (
    echo %%i | findstr /i "Up" >nul
    if not errorlevel 1 (
        goto :success
    )
)

:: Αποτυχία εκκίνησης
echo ❌ Πρόβλημα κατά την εκκίνηση
echo 📋 Container Logs:
docker logs solar-prediction-system 2>nul
echo.
echo 💡 Συμβουλές αντιμετώπισης προβλημάτων:
echo    • Ελέγξτε αν τα ports είναι ελεύθερα
echo    • Βεβαιωθείτε ότι έχετε αρκετή μνήμη (4GB+)
echo    • Δοκιμάστε: docker restart solar-prediction-system
echo.
pause
exit /b 1

:success
echo ✅ Το Solar Prediction System ξεκίνησε επιτυχώς!
echo.
echo 🌐 Διαθέσιμες υπηρεσίες:
echo    • Web Interface: http://localhost:8100
echo    • API Documentation: http://localhost:8100/docs
echo    • PostgreSQL: localhost:5432 (postgres/postgres)
echo    • Telegram Bot: Port 8109
echo    • Enhanced Billing: Port 8110
echo.
echo 📊 Για έλεγχο κατάστασης:
echo    docker logs solar-prediction-system
echo.
echo 🛑 Για διακοπή:
echo    docker stop solar-prediction-system
echo.
echo 🔄 Για επανεκκίνηση:
echo    docker start solar-prediction-system
echo.
echo 🎉 Εγκατάσταση ολοκληρώθηκε επιτυχώς!
echo.
echo 💡 Χρήσιμες εντολές:
echo    • Άνοιγμα web interface: start http://localhost:8100
echo    • Έλεγχος logs: docker logs solar-prediction-system
echo    • Πρόσβαση στο container: docker exec -it solar-prediction-system bash
echo.

:: Προσφορά άνοιγμα web interface
set /p "OPEN_WEB=Θέλετε να ανοίξετε το web interface τώρα; (y/N): "
if /i "!OPEN_WEB!"=="y" (
    start http://localhost:8100
)

echo.
pause
