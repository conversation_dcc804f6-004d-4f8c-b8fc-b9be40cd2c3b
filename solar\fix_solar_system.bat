@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Fix and Diagnostic Script

echo.
echo Solar Prediction System - Fix and Diagnostic Tool
echo ================================================
echo.

:: Check if container is running
docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" | findstr /i "Up" >nul
if errorlevel 1 (
    echo ERROR: Container is not running
    echo Starting container...
    docker start solar-prediction-system
    if errorlevel 1 (
        echo ERROR: Failed to start container
        pause
        exit /b 1
    )
    echo Waiting for container to start...
    timeout /t 30 /nobreak >nul
)

echo SUCCESS: Container is running

:: Fix database connection issue
echo.
echo Fixing database connection configuration...
docker exec solar-prediction-system bash -c "sed -i 's/5433/5432/g' /app/.env"
echo Database port configuration fixed

:: Check database connection
echo.
echo Testing database connection...
docker exec solar-prediction-system bash -c "pg_isready -U postgres" >nul
if errorlevel 1 (
    echo ERROR: Database is not ready
    echo Restarting container...
    docker restart solar-prediction-system
    echo Waiting for restart...
    timeout /t 30 /nobreak >nul
) else (
    echo SUCCESS: Database is ready
)

:: Check web interface
echo.
echo Testing web interface...
curl -s -o nul -w "%%{http_code}" http://localhost:8100 | findstr "200" >nul
if errorlevel 1 (
    echo WARNING: Web interface may have issues
) else (
    echo SUCCESS: Web interface is responding
)

:: Start Telegram bot manually
echo.
echo Starting Telegram bot...
docker exec -d solar-prediction-system bash -c "cd /app && nohup python simple_telegram_bot.py > /app/logs/telegram_bot.log 2>&1 &"

:: Wait a bit and check
timeout /t 10 /nobreak >nul

:: Check Telegram bot status
docker exec solar-prediction-system bash -c "tail -5 /app/logs/telegram_bot.log 2>/dev/null" | findstr "ERROR\|FAILED\|Exception" >nul
if not errorlevel 1 (
    echo WARNING: Telegram bot may have issues
    echo Trying alternative bot...
    docker exec -d solar-prediction-system bash -c "cd /app && nohup python simple_working_telegram_bot.py > /app/logs/telegram_bot_alt.log 2>&1 &"
) else (
    echo SUCCESS: Telegram bot started
)

:: Show system status
echo.
echo System Status Summary:
echo =====================

:: Container status
echo Container Status:
docker ps --filter "name=solar-prediction-system" --format "  {{.Status}}"

:: Database status
echo.
echo Database Status:
docker exec solar-prediction-system bash -c "pg_isready -U postgres" && echo "  PostgreSQL: Ready" || echo "  PostgreSQL: Not Ready"

:: Web interface status
echo.
echo Web Interface Status:
set WEB_STATUS=
for /f %%i in ('curl -s -o nul -w "%%{http_code}" http://localhost:8100 2^>nul') do set WEB_STATUS=%%i
if "%WEB_STATUS%"=="200" (
    echo   Web Interface: OK (HTTP 200)
) else (
    echo   Web Interface: Issues (HTTP %WEB_STATUS%)
)

:: Port status
echo.
echo Port Status:
netstat -an | findstr ":8100.*LISTENING" >nul && echo   Port 8100: Open || echo   Port 8100: Closed
netstat -an | findstr ":8109.*LISTENING" >nul && echo   Port 8109: Open || echo   Port 8109: Closed
netstat -an | findstr ":8110.*LISTENING" >nul && echo   Port 8110: Open || echo   Port 8110: Closed
netstat -an | findstr ":5432.*LISTENING" >nul && echo   Port 5432: Open || echo   Port 5432: Closed

:: Recent logs
echo.
echo Recent Application Logs:
echo ------------------------
docker exec solar-prediction-system bash -c "tail -10 /app/logs/production_app.log 2>/dev/null" | findstr -v "INFO.*GET"

:: Telegram bot logs
echo.
echo Telegram Bot Status:
echo -------------------
docker exec solar-prediction-system bash -c "tail -5 /app/logs/telegram_bot.log 2>/dev/null || echo 'No telegram bot log found'"

echo.
echo Available Services:
echo ==================
echo - Web Interface: http://localhost:8100
echo - API Documentation: http://localhost:8100/docs
echo - PostgreSQL: localhost:5432 (postgres/postgres)
echo - Telegram Bot: Should be running on port 8109
echo - Enhanced Billing: Port 8110
echo.

echo Management Commands:
echo ====================
echo - View logs: docker logs solar-prediction-system
echo - Restart system: docker restart solar-prediction-system
echo - Stop system: docker stop solar-prediction-system
echo - Access container: docker exec -it solar-prediction-system bash
echo.

set /p "OPEN_WEB=Open web interface now? (y/N): "
if /i "!OPEN_WEB!"=="y" (
    start http://localhost:8100
)

echo.
echo Fix and diagnostic completed!
pause
