@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Fix Production Issues
:: This script fixes the remaining issues after containerization

echo.
echo Solar Prediction System - Production Issues Fix
echo ==============================================
echo.

echo Step 1: Fixing Enhanced Billing Service hostname issues...
docker exec solar-prediction-system bash -c "sed -i \"s/'host': 'postgres'/'host': 'localhost'/g\" /app/enhanced_billing_service.py"
docker exec solar-prediction-system bash -c "sed -i \"s/'host': 'postgres'/'host': 'localhost'/g\" /app/scripts/frontend_system/enhanced_billing_system.py"
echo SUCCESS: Billing service hostnames fixed

echo.
echo Step 2: Fixing all remaining port issues (5433 -> 5432)...
docker exec solar-prediction-system bash -c "find /app -name '*.py' -exec sed -i 's/:5433/:5432/g' {} \;"
docker exec solar-prediction-system bash -c "find /app -name '*.py' -exec sed -i 's/5433/5432/g' {} \;"
echo SUCCESS: All port references fixed

echo.
echo Step 3: Restarting Enhanced Billing Service with fixes...
docker exec -d solar-prediction-system bash -c "cd /app && nohup python enhanced_billing_service.py > /app/logs/billing_fixed.log 2>&1 &"
timeout /t 10 /nobreak >nul
echo SUCCESS: Enhanced Billing Service restarted

echo.
echo Step 4: Testing Fixed APIs...

:: Test ROI API
echo Testing ROI API...
curl -s http://localhost:8110/billing/enhanced/roi/system1 | findstr "calculated" >nul
if errorlevel 1 (
    echo WARNING: ROI API may still have issues
) else (
    echo SUCCESS: ROI API working with real data
)

:: Test Daily Cost API
echo Testing Daily Cost API...
curl -s http://localhost:8110/billing/enhanced/cost/system1 | findstr "success" >nul
if errorlevel 1 (
    echo WARNING: Daily Cost API may have issues
) else (
    echo SUCCESS: Daily Cost API responding
)

:: Test Tariffs API
echo Testing Tariffs API...
curl -s http://localhost:8110/billing/enhanced/tariffs | findstr "success" >nul
if errorlevel 1 (
    echo WARNING: Tariffs API may have issues
) else (
    echo SUCCESS: Tariffs API working
)

echo.
echo Step 5: Checking Telegram Bot Status...
docker exec solar-prediction-system bash -c "tail -3 /app/logs/official_telegram_bot.log | grep -v 'HTTP Request'" | findstr "ERROR\|Exception" >nul
if not errorlevel 1 (
    echo WARNING: Telegram bot may have issues
) else (
    echo SUCCESS: Telegram bot running normally
)

echo.
echo Step 6: System Status Summary...
echo ================================

:: Main App Status
curl -s -o nul -w "%%{http_code}" http://localhost:8100/health | findstr "200" >nul
if errorlevel 1 (
    echo Main App (8100): NOT OK
) else (
    echo Main App (8100): OK
)

:: Enhanced Billing Status
curl -s -o nul -w "%%{http_code}" http://localhost:8110/health | findstr "200" >nul
if errorlevel 1 (
    echo Enhanced Billing (8110): NOT OK
) else (
    echo Enhanced Billing (8110): OK
)

:: Database Status
docker exec solar-prediction-system bash -c "pg_isready -U postgres" >nul 2>&1
if errorlevel 1 (
    echo Database (5432): NOT OK
) else (
    echo Database (5432): OK
)

echo.
echo Step 7: API Functionality Test...
echo =================================

:: Get current solar data
echo Testing Solar Data API...
curl -s http://localhost:8100/api/v1/solax/current | findstr "success" >nul
if errorlevel 1 (
    echo Solar Data API: FAILED
) else (
    echo Solar Data API: OK
)

:: Get weather data
echo Testing Weather API...
curl -s http://localhost:8100/api/v1/weather/current | findstr "temperature" >nul
if errorlevel 1 (
    echo Weather API: FAILED
) else (
    echo Weather API: OK
)

:: Get predictions
echo Testing Predictions API...
curl -s http://localhost:8100/api/v1/predictions/recent | findstr "predicted_ac_power" >nul
if errorlevel 1 (
    echo Predictions API: FAILED
) else (
    echo Predictions API: OK
)

echo.
echo Step 8: Detailed API Results...
echo ===============================

echo ROI System1 Results:
curl -s http://localhost:8110/billing/enhanced/roi/system1 | findstr "annual_roi_percent\|payback_years\|status"

echo.
echo Daily Cost System1 Results:
curl -s http://localhost:8110/billing/enhanced/cost/system1 | findstr "total_cost\|energy_cost\|status"

echo.
echo Current Solar Production:
curl -s http://localhost:8100/api/v1/solax/current | findstr "acpower\|soc"

echo.
echo ========================================
echo    PRODUCTION ISSUES FIX COMPLETED
echo ========================================
echo.
echo Fixed Issues:
echo - Enhanced Billing hostname (postgres -> localhost)
echo - All port references (5433 -> 5432)
echo - Database connections
echo.
echo Remaining Issues to Check:
echo - Daily Cost calculations (may need data)
echo - ML Predictions (may need model activation)
echo.
echo Telegram Bot Test:
echo 1. Open @grlvSolarAI_bot
echo 2. Try ROI ^& Payback (should work now)
echo 3. Try Daily Cost (may show "no data")
echo 4. Try System Data (should show real values)
echo.

set /p "TEST_BOT=Test Telegram bot now? (y/N): "
if /i "!TEST_BOT!"=="y" (
    echo Opening Telegram...
    start https://web.telegram.org/
)

echo.
echo Fix completed! Check Telegram bot functionality.
pause
