{"id": "ea37df4a46e078c05c569a82e02a54eb48763fa09da735c5ab81ddd8706ae1aa", "parent": "192b6cc8449dedb31e876684925aa443a79bb18b2dd0ad8a5ed24ee273edd141", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}