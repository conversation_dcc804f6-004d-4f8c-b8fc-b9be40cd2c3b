# Solar Prediction System - Windows Docker Installation Script
# Χρήση: .\install_solar_docker.ps1
# Απαιτεί: PowerShell 5.0+ και Docker Desktop

param(
    [switch]$Force,
    [string]$ImageFile = "solar-prediction-complete.tar.gz"
)

# Ρύθμιση για UTF-8 encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 Solar Prediction System - Windows Docker Installation" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Green

# Έλεγχος αν τρέχει ως Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "⚠️  Συνιστάται να τρέξετε ως Administrator για καλύτερη απόδοση" -ForegroundColor Yellow
}

# Έλεγχος PowerShell version
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "❌ Απαιτείται PowerShell 5.0 ή νεότερη έκδοση" -ForegroundColor Red
    Write-Host "Τρέχουσα έκδοση: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    exit 1
}

Write-Host "✅ PowerShell έκδοση: $($PSVersionTable.PSVersion)" -ForegroundColor Green

# Έλεγχος Docker
try {
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not found"
    }
    Write-Host "✅ Docker βρέθηκε: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Το Docker δεν είναι εγκατεστημένο ή δεν τρέχει" -ForegroundColor Red
    Write-Host "Παρακαλώ:" -ForegroundColor Yellow
    Write-Host "1. Εγκαταστήστε το Docker Desktop από: https://docs.docker.com/desktop/windows/" -ForegroundColor Yellow
    Write-Host "2. Βεβαιωθείτε ότι το Docker Desktop τρέχει" -ForegroundColor Yellow
    Write-Host "3. Επανεκκινήστε το PowerShell" -ForegroundColor Yellow
    exit 1
}

# Έλεγχος Docker daemon
try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker daemon not running"
    }
    Write-Host "✅ Docker daemon τρέχει" -ForegroundColor Green
} catch {
    Write-Host "❌ Το Docker daemon δεν τρέχει" -ForegroundColor Red
    Write-Host "Παρακαλώ ξεκινήστε το Docker Desktop" -ForegroundColor Yellow
    exit 1
}

# Έλεγχος αρχείου image
if (-not (Test-Path $ImageFile)) {
    Write-Host "❌ Το αρχείο '$ImageFile' δεν βρέθηκε" -ForegroundColor Red
    Write-Host "Παρακαλώ βεβαιωθείτε ότι το αρχείο βρίσκεται στον ίδιο φάκελο" -ForegroundColor Yellow
    exit 1
}

$imageSize = [math]::Round((Get-Item $ImageFile).Length / 1GB, 2)
Write-Host "✅ Αρχείο image βρέθηκε: $ImageFile ($imageSize GB)" -ForegroundColor Green

# Έλεγχος ελεύθερου χώρου
$drive = (Get-Location).Drive
$freeSpace = [math]::Round((Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$($drive.Name)'").FreeSpace / 1GB, 2)
$requiredSpace = $imageSize * 3  # Image + extracted + Docker layers

if ($freeSpace -lt $requiredSpace) {
    Write-Host "⚠️  Προειδοποίηση: Ελεύθερος χώρος $freeSpace GB, απαιτούνται ~$requiredSpace GB" -ForegroundColor Yellow
    if (-not $Force) {
        $continue = Read-Host "Θέλετε να συνεχίσετε; (y/N)"
        if ($continue -notmatch '^[Yy]$') {
            Write-Host "❌ Εγκατάσταση ακυρώθηκε" -ForegroundColor Red
            exit 1
        }
    }
}

# Έλεγχος ports
$portsToCheck = @(5432, 8100, 8109, 8110)
$busyPorts = @()

foreach ($port in $portsToCheck) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        $busyPorts += $port
    }
}

if ($busyPorts.Count -gt 0) {
    Write-Host "⚠️  Τα ακόλουθα ports είναι σε χρήση: $($busyPorts -join ', ')" -ForegroundColor Yellow
    Write-Host "Αυτό μπορεί να προκαλέσει προβλήματα κατά την εκκίνηση" -ForegroundColor Yellow
    if (-not $Force) {
        $continue = Read-Host "Θέλετε να συνεχίσετε; (y/N)"
        if ($continue -notmatch '^[Yy]$') {
            Write-Host "❌ Εγκατάσταση ακυρώθηκε" -ForegroundColor Red
            exit 1
        }
    }
}

# Αποσυμπίεση αρχείου
Write-Host "📦 Αποσυμπίεση αρχείου..." -ForegroundColor Cyan

# Έλεγχος αν υπάρχει 7-Zip ή άλλο εργαλείο αποσυμπίεσης
$tarFile = $ImageFile -replace '\.gz$', ''

try {
    # Δοκιμή με 7-Zip
    if (Get-Command "7z" -ErrorAction SilentlyContinue) {
        & 7z x $ImageFile -y | Out-Null
        if ($LASTEXITCODE -ne 0) { throw "7z failed" }
    }
    # Δοκιμή με tar (Windows 10 1903+)
    elseif (Get-Command "tar" -ErrorAction SilentlyContinue) {
        & tar -xzf $ImageFile
        if ($LASTEXITCODE -ne 0) { throw "tar failed" }
    }
    # Δοκιμή με PowerShell (αργό αλλά λειτουργεί)
    else {
        Write-Host "Χρήση PowerShell για αποσυμπίεση (μπορεί να πάρει λίγο χρόνο)..." -ForegroundColor Yellow
        
        # Αποσυμπίεση gzip
        $gzipStream = New-Object System.IO.FileStream($ImageFile, [System.IO.FileMode]::Open)
        $gzipDecompressor = New-Object System.IO.Compression.GzipStream($gzipStream, [System.IO.Compression.CompressionMode]::Decompress)
        $tarStream = New-Object System.IO.FileStream($tarFile, [System.IO.FileMode]::Create)
        
        $gzipDecompressor.CopyTo($tarStream)
        
        $tarStream.Close()
        $gzipDecompressor.Close()
        $gzipStream.Close()
    }
    
    Write-Host "✅ Αποσυμπίεση ολοκληρώθηκε" -ForegroundColor Green
} catch {
    Write-Host "❌ Σφάλμα κατά την αποσυμπίεση: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Παρακαλώ εγκαταστήστε το 7-Zip ή χρησιμοποιήστε Windows 10 1903+" -ForegroundColor Yellow
    exit 1
}

# Φόρτωση στο Docker
Write-Host "🐳 Φόρτωση Docker image..." -ForegroundColor Cyan

try {
    docker load -i $tarFile
    if ($LASTEXITCODE -ne 0) {
        throw "Docker load failed"
    }
    Write-Host "✅ Docker image φορτώθηκε επιτυχώς" -ForegroundColor Green
} catch {
    Write-Host "❌ Σφάλμα κατά τη φόρτωση του Docker image" -ForegroundColor Red
    exit 1
}

# Καθαρισμός προσωρινών αρχείων
Write-Host "🧹 Καθαρισμός προσωρινών αρχείων..." -ForegroundColor Cyan
try {
    Remove-Item $tarFile -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Καθαρισμός ολοκληρώθηκε" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Δεν ήταν δυνατός ο καθαρισμός του $tarFile" -ForegroundColor Yellow
}

# Έλεγχος αν το container υπάρχει ήδη
$existingContainer = docker ps -a --filter "name=solar-prediction-system" --format "{{.Names}}" 2>$null

if ($existingContainer -eq "solar-prediction-system") {
    Write-Host "⚠️  Το container 'solar-prediction-system' υπάρχει ήδη" -ForegroundColor Yellow
    if (-not $Force) {
        $remove = Read-Host "Θέλετε να το αφαιρέσετε και να δημιουργήσετε νέο; (y/N)"
        if ($remove -notmatch '^[Yy]$') {
            Write-Host "❌ Εγκατάσταση ακυρώθηκε" -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host "🗑️  Αφαίρεση παλιού container..." -ForegroundColor Cyan
    docker stop solar-prediction-system 2>$null | Out-Null
    docker rm solar-prediction-system 2>$null | Out-Null
}

# Εκκίνηση container
Write-Host "🚀 Εκκίνηση Solar Prediction System..." -ForegroundColor Cyan

try {
    docker run -d `
        --name solar-prediction-system `
        --restart unless-stopped `
        -p 5432:5432 `
        -p 8100:8100 `
        -p 8109:8109 `
        -p 8110:8110 `
        solar-prediction-complete:latest
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker run failed"
    }
} catch {
    Write-Host "❌ Σφάλμα κατά την εκκίνηση του container" -ForegroundColor Red
    Write-Host "Logs:" -ForegroundColor Yellow
    docker logs solar-prediction-system 2>$null
    exit 1
}

# Αναμονή για εκκίνηση
Write-Host "⏳ Αναμονή για εκκίνηση υπηρεσιών (30 δευτερόλεπτα)..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Έλεγχος κατάστασης
$containerStatus = docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" 2>$null

if ($containerStatus -like "*Up*") {
    Write-Host "✅ Το Solar Prediction System ξεκίνησε επιτυχώς!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Διαθέσιμες υπηρεσίες:" -ForegroundColor Cyan
    Write-Host "   • Web Interface: http://localhost:8100" -ForegroundColor White
    Write-Host "   • API Documentation: http://localhost:8100/docs" -ForegroundColor White
    Write-Host "   • PostgreSQL: localhost:5432 (postgres/postgres)" -ForegroundColor White
    Write-Host "   • Telegram Bot: Port 8109" -ForegroundColor White
    Write-Host "   • Enhanced Billing: Port 8110" -ForegroundColor White
    Write-Host ""
    Write-Host "📊 Για έλεγχο κατάστασης:" -ForegroundColor Cyan
    Write-Host "   docker logs solar-prediction-system" -ForegroundColor White
    Write-Host ""
    Write-Host "🛑 Για διακοπή:" -ForegroundColor Cyan
    Write-Host "   docker stop solar-prediction-system" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 Για επανεκκίνηση:" -ForegroundColor Cyan
    Write-Host "   docker start solar-prediction-system" -ForegroundColor White
    
    # Δοκιμή σύνδεσης στο web interface
    Write-Host ""
    Write-Host "🔍 Έλεγχος web interface..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8100" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Web interface προσβάσιμο" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️  Web interface δεν είναι ακόμα έτοιμο (δοκιμάστε σε λίγα λεπτά)" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ Πρόβλημα κατά την εκκίνηση" -ForegroundColor Red
    Write-Host "📋 Container Logs:" -ForegroundColor Yellow
    docker logs solar-prediction-system 2>$null
    Write-Host ""
    Write-Host "💡 Συμβουλές αντιμετώπισης προβλημάτων:" -ForegroundColor Cyan
    Write-Host "   • Ελέγξτε αν τα ports είναι ελεύθερα" -ForegroundColor White
    Write-Host "   • Βεβαιωθείτε ότι έχετε αρκετή μνήμη (4GB+)" -ForegroundColor White
    Write-Host "   • Δοκιμάστε: docker restart solar-prediction-system" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "🎉 Εγκατάσταση ολοκληρώθηκε επιτυχώς!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 Χρήσιμες εντολές:" -ForegroundColor Cyan
Write-Host "   • Άνοιγμα web interface: Start-Process 'http://localhost:8100'" -ForegroundColor White
Write-Host "   • Έλεγχος logs: docker logs solar-prediction-system" -ForegroundColor White
Write-Host "   • Πρόσβαση στο container: docker exec -it solar-prediction-system bash" -ForegroundColor White
