@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Windows Docker Installation Script (Batch)
:: Usage: install_solar_docker.bat

echo.
echo Solar Prediction System - Windows Docker Installation
echo ====================================================
echo.

:: Check Docker
echo Checking Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not running
    echo.
    echo Please:
    echo 1. Install Docker Desktop from: https://docs.docker.com/desktop/windows/
    echo 2. Make sure Docker Desktop is running
    echo 3. Restart Command Prompt
    echo.
    pause
    exit /b 1
)

:: Check Docker daemon
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker daemon is not running
    echo Please start Docker Desktop
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Docker found and running

:: Check image file
set IMAGE_FILE=solar-prediction-complete.tar.gz
if not exist "%IMAGE_FILE%" (
    echo ERROR: File '%IMAGE_FILE%' not found
    echo Please make sure the file is in the same folder
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Image file found: %IMAGE_FILE%

:: Check for decompression tool
set DECOMPRESS_TOOL=
where tar >nul 2>&1
if not errorlevel 1 (
    set DECOMPRESS_TOOL=tar
    goto :decompress_found
)

where 7z >nul 2>&1
if not errorlevel 1 (
    set DECOMPRESS_TOOL=7z
    goto :decompress_found
)

echo ERROR: No decompression tool found
echo Please install:
echo - 7-Zip from: https://www.7-zip.org/
echo - Or use Windows 10 1903+ which has built-in tar
echo.
pause
exit /b 1

:decompress_found
echo SUCCESS: Decompression tool: %DECOMPRESS_TOOL%

:: Decompress
echo.
echo Decompressing file...
set TAR_FILE=solar-prediction-complete.tar

if "%DECOMPRESS_TOOL%"=="tar" (
    tar -xzf "%IMAGE_FILE%"
) else if "%DECOMPRESS_TOOL%"=="7z" (
    7z x "%IMAGE_FILE%" -y >nul
)

if errorlevel 1 (
    echo ERROR: Failed to decompress
    pause
    exit /b 1
)

if not exist "%TAR_FILE%" (
    echo ERROR: File %TAR_FILE% was not created
    pause
    exit /b 1
)

echo SUCCESS: Decompression completed

:: Load into Docker
echo.
echo Loading Docker image...
docker load -i "%TAR_FILE%"
if errorlevel 1 (
    echo ERROR: Failed to load Docker image
    pause
    exit /b 1
)

echo SUCCESS: Docker image loaded successfully

:: Cleanup
echo.
echo Cleaning up temporary files...
del "%TAR_FILE%" >nul 2>&1
echo SUCCESS: Cleanup completed

:: Check if container already exists
for /f "tokens=*" %%i in ('docker ps -a --filter "name=solar-prediction-system" --format "{{.Names}}" 2^>nul') do (
    if "%%i"=="solar-prediction-system" (
        echo.
        echo WARNING: Container 'solar-prediction-system' already exists
        set /p "REMOVE=Do you want to remove it and create a new one? (y/N): "
        if /i "!REMOVE!"=="y" (
            echo Removing old container...
            docker stop solar-prediction-system >nul 2>&1
            docker rm solar-prediction-system >nul 2>&1
        ) else (
            echo Installation cancelled
            pause
            exit /b 1
        )
    )
)

:: Start container
echo.
echo Starting Solar Prediction System...
docker run -d ^
    --name solar-prediction-system ^
    --restart unless-stopped ^
    -p 5432:5432 ^
    -p 8100:8100 ^
    -p 8109:8109 ^
    -p 8110:8110 ^
    solar-prediction-complete:latest

if errorlevel 1 (
    echo ERROR: Failed to start container
    echo Logs:
    docker logs solar-prediction-system 2>nul
    pause
    exit /b 1
)

:: Wait for startup
echo Waiting for services to start (30 seconds)...
timeout /t 30 /nobreak >nul

:: Check status
for /f "tokens=*" %%i in ('docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" 2^>nul') do (
    echo %%i | findstr /i "Up" >nul
    if not errorlevel 1 (
        goto :success
    )
)

:: Startup failure
echo ERROR: Problem during startup
echo Container Logs:
docker logs solar-prediction-system 2>nul
echo.
echo Troubleshooting tips:
echo    - Check if ports are free
echo    - Make sure you have enough memory (4GB+)
echo    - Try: docker restart solar-prediction-system
echo.
pause
exit /b 1

:success
echo SUCCESS: Solar Prediction System started successfully!
echo.
echo Available services:
echo    - Web Interface: http://localhost:8100
echo    - API Documentation: http://localhost:8100/docs
echo    - PostgreSQL: localhost:5432 (postgres/postgres)
echo    - Telegram Bot: Port 8109
echo    - Enhanced Billing: Port 8110
echo.
echo To check status:
echo    docker logs solar-prediction-system
echo.
echo To stop:
echo    docker stop solar-prediction-system
echo.
echo To restart:
echo    docker start solar-prediction-system
echo.
echo Installation completed successfully!
echo.
echo Useful commands:
echo    - Open web interface: start http://localhost:8100
echo    - Check logs: docker logs solar-prediction-system
echo    - Access container: docker exec -it solar-prediction-system bash
echo.

:: Offer to open web interface
set /p "OPEN_WEB=Do you want to open the web interface now? (y/N): "
if /i "!OPEN_WEB!"=="y" (
    start http://localhost:8100
)

echo.
pause
