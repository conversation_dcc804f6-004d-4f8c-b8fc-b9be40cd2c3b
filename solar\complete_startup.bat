@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Complete Startup Script
:: This script starts all services in the correct order with port forwarding

echo.
echo Solar Prediction System - Complete Startup
echo =========================================
echo.

:: Wait for container to be fully ready
echo Waiting for container to be fully ready...
timeout /t 30 /nobreak >nul

:: Step 1: Create and start port forwarder (5433 -> 5432)
echo.
echo Step 1: Setting up port forwarding (5433 -> 5432)...
docker exec solar-prediction-system bash -c "cat > /app/port_forwarder.py << 'EOF'
#!/usr/bin/env python3
import socket
import threading
import time

def forward_connection(client_socket, target_host, target_port):
    try:
        target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        target_socket.connect((target_host, target_port))
        
        def forward_data(source, destination):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
            except:
                pass
            finally:
                source.close()
                destination.close()
        
        thread1 = threading.Thread(target=forward_data, args=(client_socket, target_socket))
        thread2 = threading.Thread(target=forward_data, args=(target_socket, client_socket))
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
        
    except Exception as e:
        print(f'Connection error: {e}')
        client_socket.close()

def start_port_forwarder(listen_port, target_host, target_port):
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind(('0.0.0.0', listen_port))
    server_socket.listen(5)
    
    print(f'Port forwarder listening on port {listen_port}, forwarding to {target_host}:{target_port}')
    
    while True:
        try:
            client_socket, client_address = server_socket.accept()
            print(f'Connection from {client_address}')
            thread = threading.Thread(target=forward_connection, args=(client_socket, target_host, target_port))
            thread.daemon = True
            thread.start()
        except Exception as e:
            print(f'Server error: {e}')
            time.sleep(1)

if __name__ == '__main__':
    start_port_forwarder(5433, 'localhost', 5432)
EOF"

:: Start port forwarder
docker exec -d solar-prediction-system bash -c "cd /app && nohup python port_forwarder.py > /app/logs/port_forwarder.log 2>&1 &"

:: Wait for port forwarder
timeout /t 5 /nobreak >nul

:: Test port forwarder
docker exec solar-prediction-system bash -c "pg_isready -h localhost -p 5433 -U postgres" >nul 2>&1
if errorlevel 1 (
    echo WARNING: Port forwarder may not be ready
) else (
    echo SUCCESS: Port forwarder (5433 -> 5432) is working
)

:: Step 2: Start Enhanced Billing Service
echo.
echo Step 2: Starting Enhanced Billing Service...
docker exec -d solar-prediction-system bash -c "cd /app && nohup python enhanced_billing_service.py > /app/logs/billing_service.log 2>&1 &"

:: Wait for billing service
timeout /t 10 /nobreak >nul

:: Test billing service
curl -s -o nul -w "%%{http_code}" http://localhost:8110/health | findstr "200" >nul
if errorlevel 1 (
    echo WARNING: Billing service may not be ready
) else (
    echo SUCCESS: Enhanced Billing Service (8110) is running
)

:: Step 3: Start Telegram Bot
echo.
echo Step 3: Starting Telegram Bot...
docker exec -d solar-prediction-system bash -c "cd /app && nohup python simple_working_telegram_bot.py > /app/logs/telegram_bot_final.log 2>&1 &"

:: Wait for telegram bot
timeout /t 15 /nobreak >nul

:: Step 4: Comprehensive Service Check
echo.
echo Step 4: Comprehensive Service Check
echo ===================================

:: Main Application
curl -s -o nul -w "%%{http_code}" http://localhost:8100/health | findstr "200" >nul
if errorlevel 1 (
    echo   Main App (8100): NOT READY
) else (
    echo   Main App (8100): OK
)

:: Enhanced Billing
curl -s -o nul -w "%%{http_code}" http://localhost:8110/health | findstr "200" >nul
if errorlevel 1 (
    echo   Billing Service (8110): NOT READY
) else (
    echo   Billing Service (8110): OK
)

:: Database (original port)
docker exec solar-prediction-system bash -c "pg_isready -U postgres" >nul 2>&1
if errorlevel 1 (
    echo   Database (5432): NOT READY
) else (
    echo   Database (5432): OK
)

:: Database (forwarded port)
docker exec solar-prediction-system bash -c "pg_isready -h localhost -p 5433 -U postgres" >nul 2>&1
if errorlevel 1 (
    echo   Database Forward (5433): NOT READY
) else (
    echo   Database Forward (5433): OK
)

:: Step 5: API Endpoint Tests
echo.
echo Step 5: API Endpoint Tests
echo ==========================

:: Test main API endpoints
curl -s http://localhost:8100/api/v1/solax/current | findstr "success" >nul
if errorlevel 1 (
    echo   Solax Data API: FAILED
) else (
    echo   Solax Data API: OK
)

curl -s http://localhost:8100/api/v1/weather/current | findstr "temperature\|weather" >nul
if errorlevel 1 (
    echo   Weather API: FAILED
) else (
    echo   Weather API: OK
)

:: Test billing endpoints
curl -s http://localhost:8110/billing/enhanced/tariffs | findstr "success" >nul
if errorlevel 1 (
    echo   Billing Tariffs: FAILED
) else (
    echo   Billing Tariffs: OK
)

curl -s http://localhost:8110/billing/enhanced/roi/system1 | findstr "system_id" >nul
if errorlevel 1 (
    echo   Billing ROI: FAILED
) else (
    echo   Billing ROI: OK
)

curl -s http://localhost:8110/billing/enhanced/cost/system1 | findstr "system_id\|cost" >nul
if errorlevel 1 (
    echo   Billing Cost: FAILED
) else (
    echo   Billing Cost: OK
)

:: Step 6: Show Recent Logs
echo.
echo Step 6: Recent System Logs
echo ==========================

echo Main Application:
docker exec solar-prediction-system bash -c "tail -3 /app/logs/production_app.log 2>/dev/null" | findstr -v "INFO.*GET"

echo.
echo Port Forwarder:
docker exec solar-prediction-system bash -c "tail -2 /app/logs/port_forwarder.log 2>/dev/null"

echo.
echo Billing Service:
docker exec solar-prediction-system bash -c "tail -2 /app/logs/billing_service.log 2>/dev/null"

echo.
echo Telegram Bot:
docker exec solar-prediction-system bash -c "tail -3 /app/logs/telegram_bot_final.log 2>/dev/null | grep -v 'HTTP Request'"

:: Final Summary
echo.
echo ========================================
echo    SOLAR PREDICTION SYSTEM READY!
echo ========================================
echo.
echo Available Services:
echo - Web Interface: http://localhost:8100
echo - API Documentation: http://localhost:8100/docs
echo - Enhanced Billing: http://localhost:8110
echo - Telegram Bot: @grlvSolarAI_bot
echo - Database: localhost:5432 (internal)
echo - Database: localhost:5433 (forwarded for compatibility)
echo.
echo Telegram Bot Commands:
echo - /start - Start bot and show menu
echo - /menu - Show main menu
echo - Try all menu options - they should work now!
echo.

set /p "OPEN_TELEGRAM=Open Telegram Web to test bot? (y/N): "
if /i "!OPEN_TELEGRAM!"=="y" (
    echo.
    echo Opening Telegram Web...
    echo 1. Search for: @grlvSolarAI_bot
    echo 2. Send: /start
    echo 3. Test ALL menu options!
    start https://web.telegram.org/
)

echo.
echo Startup completed successfully!
pause
