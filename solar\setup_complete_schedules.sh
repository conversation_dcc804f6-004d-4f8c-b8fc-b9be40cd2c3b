#!/bin/bash

# Complete Solar Prediction System Schedules Setup
echo "🔧 Setting up Complete Solar Prediction System Schedules"
echo "======================================================="

# Start cron service
echo "📅 Starting cron service..."
service cron start

# Create comprehensive crontab
echo "📝 Creating comprehensive crontab..."
cat > /tmp/solar_complete_crontab << 'EOF'
# Solar Prediction System - Complete Schedules
# Generated: 2025-06-29

# =============================================================================
# REAL-TIME DATA COLLECTION (Every 5 minutes)
# =============================================================================
*/5 * * * * cd /app && python scripts/data/solax_collector.py >> /app/logs/solax_cron.log 2>&1

# =============================================================================
# WEATHER DATA COLLECTION (Every hour)
# =============================================================================
0 * * * * cd /app && python scripts/data/weather_collector.py >> /app/logs/weather_cron.log 2>&1

# =============================================================================
# DAILY PREDICTIONS REFRESH (6:00 AM)
# =============================================================================
0 6 * * * cd /app && python scripts/prediction/ml_ensemble_forecast.py --system_id system1 --hours 168 >> /app/logs/predictions_cron.log 2>&1

# =============================================================================
# DAILY NASA POWER DATA (6:30 AM)
# =============================================================================
30 6 * * * cd /app && python scripts/scheduled/daily_nasa_power_collection.py >> /app/logs/nasa_cron.log 2>&1

# =============================================================================
# WEEKLY CAMS DATA ATTEMPT (7:00 AM Sunday)
# =============================================================================
0 7 * * 0 cd /app && python scripts/data/cams_solar_collector.py >> /app/logs/cams_weekly_cron.log 2>&1

# =============================================================================
# SYSTEM HEALTH CHECK (Every 6 hours)
# =============================================================================
0 */6 * * * cd /app && python -c "
import requests
try:
    r = requests.get('http://localhost:8100/api/v1/solax/current', timeout=10)
    print(f'Main API: {r.status_code}')
    r = requests.get('http://localhost:8110/billing/enhanced/roi/system1', timeout=10)
    print(f'Billing API: {r.status_code}')
except Exception as e:
    print(f'Health check failed: {e}')
" >> /app/logs/health_cron.log 2>&1

# =============================================================================
# LOG CLEANUP (Daily at 2:00 AM)
# =============================================================================
0 2 * * * find /app/logs -name "*.log" -size +100M -exec truncate -s 50M {} \; >> /app/logs/cleanup_cron.log 2>&1

EOF

# Install the crontab
echo "⚙️ Installing crontab..."
crontab /tmp/solar_complete_crontab

# Verify installation
echo "✅ Verifying crontab installation..."
echo "Current crontab:"
crontab -l

echo ""
echo "🚀 Starting Essential Background Services..."

# Start prediction scheduler service
echo "📅 Starting Prediction Scheduler Service (port 8106)..."
nohup python scripts/production/prediction_scheduler.py > /app/logs/prediction_scheduler.log 2>&1 &

# Start continuous SolaX collection (backup to cron)
echo "📊 Starting continuous SolaX collection..."
nohup bash -c "while true; do python scripts/data/solax_collector.py; sleep 300; done" > /app/logs/solax_continuous.log 2>&1 &

echo ""
echo "✅ Complete Scheduling System Setup Completed!"
echo "=============================================="
echo ""
echo "📋 SCHEDULED TASKS:"
echo "• SolaX Data: Every 5 minutes"
echo "• Weather Data: Every hour"
echo "• Predictions: Daily at 6:00 AM"
echo "• NASA POWER: Daily at 6:30 AM"
echo "• CAMS Data: Weekly (Sunday 7:00 AM)"
echo "• Health Check: Every 6 hours"
echo "• Log Cleanup: Daily at 2:00 AM"
echo ""
echo "🚀 BACKGROUND SERVICES:"
echo "• Prediction Scheduler: port 8106"
echo "• Continuous SolaX: Every 5 minutes"
echo ""
echo "📊 MONITORING:"
echo "• Cron logs: /app/logs/*_cron.log"
echo "• Service logs: /app/logs/prediction_scheduler.log"
echo "• Health logs: /app/logs/health_cron.log"
echo ""
echo "🔧 MANUAL TRIGGERS:"
echo "• Refresh predictions: curl http://localhost:8106/refresh"
echo "• Check crontab: crontab -l"
echo "• View logs: tail -f /app/logs/predictions_cron.log"
