{"id": "09ca4b2fbeb6992786f76e8646770527f1102f2bb483fa7a0334d7808163b57e", "parent": "2c80f5404d97505c78b77552794dde369f54f16ec937a3e6959918ddea78a869", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}