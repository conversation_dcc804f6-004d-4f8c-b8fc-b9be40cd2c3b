#!/bin/bash

# Solar Prediction System - Production Database Restore Script
# Χρήση: ./restore_production_backup.sh [backup_file]

set -e

# Χρώματα για output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Solar Prediction System - Database Restore${NC}"
echo "=================================================="

# Παράμετροι
BACKUP_FILE=${1:-"production_database_full_backup_20250629_193146.sql.gz"}
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5433"}
DB_USER=${DB_USER:-"postgres"}
DB_NAME=${DB_NAME:-"solar_prediction"}
PGPASSWORD=${PGPASSWORD:-"postgres"}

export PGPASSWORD

echo -e "${BLUE}📋 Παράμετροι:${NC}"
echo "  Backup File: $BACKUP_FILE"
echo "  Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo "  User: $DB_USER"
echo ""

# Έλεγχος αν υπάρχει το backup file
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}❌ Το αρχείο backup '$BACKUP_FILE' δεν βρέθηκε${NC}"
    echo ""
    echo "Διαθέσιμα backup αρχεία:"
    ls -lh production_database_full_backup_*.sql* 2>/dev/null || echo "  Κανένα backup αρχείο δεν βρέθηκε"
    exit 1
fi

echo -e "${GREEN}✅ Backup αρχείο βρέθηκε: $BACKUP_FILE${NC}"

# Έλεγχος μεγέθους
BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
echo "  Μέγεθος: $BACKUP_SIZE"

# Έλεγχος σύνδεσης στη βάση
echo ""
echo -e "${BLUE}🔍 Έλεγχος σύνδεσης βάσης δεδομένων...${NC}"
if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" >/dev/null 2>&1; then
    echo -e "${RED}❌ Δεν είναι δυνατή η σύνδεση στη βάση δεδομένων${NC}"
    echo "Παρακαλώ βεβαιωθείτε ότι:"
    echo "  • Η PostgreSQL τρέχει"
    echo "  • Οι παράμετροι σύνδεσης είναι σωστές"
    echo "  • Το Docker container τρέχει (αν χρησιμοποιείται)"
    exit 1
fi

echo -e "${GREEN}✅ Σύνδεση βάσης δεδομένων επιτυχής${NC}"

# Έλεγχος αν η βάση υπάρχει
echo ""
echo -e "${BLUE}🔍 Έλεγχος βάσης δεδομένων...${NC}"
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    echo -e "${YELLOW}⚠️  Η βάση δεδομένων '$DB_NAME' υπάρχει ήδη${NC}"
    
    # Μέτρηση εγγραφών
    EXISTING_RECORDS=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
        SELECT COALESCE(SUM(n_live_tup), 0) 
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public';" 2>/dev/null || echo "0")
    
    echo "  Τρέχουσες εγγραφές: $EXISTING_RECORDS"
    echo ""
    
    read -p "Θέλετε να συνεχίσετε; Αυτό θα διαγράψει όλα τα υπάρχοντα δεδομένα (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}❌ Επαναφορά ακυρώθηκε${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}🗑️  Διαγραφή υπάρχουσας βάσης...${NC}"
    dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
fi

# Δημιουργία νέας βάσης
echo -e "${BLUE}🆕 Δημιουργία νέας βάσης δεδομένων...${NC}"
createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
echo -e "${GREEN}✅ Βάση δεδομένων δημιουργήθηκε${NC}"

# Προετοιμασία αρχείου για restore
echo ""
echo -e "${BLUE}📦 Προετοιμασία backup αρχείου...${NC}"

TEMP_SQL_FILE=""
if [[ "$BACKUP_FILE" == *.gz ]]; then
    echo "  Αποσυμπίεση gzip αρχείου..."
    TEMP_SQL_FILE="${BACKUP_FILE%.gz}"
    gunzip -c "$BACKUP_FILE" > "$TEMP_SQL_FILE"
    SQL_FILE="$TEMP_SQL_FILE"
else
    SQL_FILE="$BACKUP_FILE"
fi

echo -e "${GREEN}✅ Αρχείο έτοιμο για restore${NC}"

# Εκτίμηση χρόνου
SQL_SIZE=$(du -h "$SQL_FILE" | cut -f1)
echo "  Μέγεθος SQL: $SQL_SIZE"
echo "  Εκτιμώμενος χρόνος: 2-5 λεπτά"

# Restore της βάσης
echo ""
echo -e "${BLUE}🔄 Επαναφορά βάσης δεδομένων...${NC}"
echo "  Αυτό μπορεί να πάρει αρκετά λεπτά..."

START_TIME=$(date +%s)

if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$SQL_FILE" >/dev/null 2>&1; then
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    echo -e "${GREEN}✅ Επαναφορά ολοκληρώθηκε επιτυχώς${NC}"
    echo "  Διάρκεια: ${DURATION} δευτερόλεπτα"
else
    echo -e "${RED}❌ Σφάλμα κατά την επαναφορά${NC}"
    
    # Καθαρισμός προσωρινών αρχείων
    if [ -n "$TEMP_SQL_FILE" ] && [ -f "$TEMP_SQL_FILE" ]; then
        rm -f "$TEMP_SQL_FILE"
    fi
    
    exit 1
fi

# Καθαρισμός προσωρινών αρχείων
if [ -n "$TEMP_SQL_FILE" ] && [ -f "$TEMP_SQL_FILE" ]; then
    echo ""
    echo -e "${BLUE}🧹 Καθαρισμός προσωρινών αρχείων...${NC}"
    rm -f "$TEMP_SQL_FILE"
    echo -e "${GREEN}✅ Καθαρισμός ολοκληρώθηκε${NC}"
fi

# Επαλήθευση επαναφοράς
echo ""
echo -e "${BLUE}🔍 Επαλήθευση επαναφοράς...${NC}"

# Μέτρηση πινάκων
TABLES_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'public';" 2>/dev/null || echo "0")

# Μέτρηση εγγραφών
RECORDS_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
    SELECT COALESCE(SUM(n_live_tup), 0) 
    FROM pg_stat_user_tables 
    WHERE schemaname = 'public';" 2>/dev/null || echo "0")

# Έλεγχος κύριων πινάκων
SOLAX_DATA_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
    SELECT COUNT(*) FROM solax_data;" 2>/dev/null || echo "0")

SOLAX_DATA2_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "
    SELECT COUNT(*) FROM solax_data2;" 2>/dev/null || echo "0")

echo "📊 Αποτελέσματα επαναφοράς:"
echo "  • Πίνακες: $TABLES_COUNT"
echo "  • Συνολικές εγγραφές: $RECORDS_COUNT"
echo "  • solax_data: $SOLAX_DATA_COUNT εγγραφές"
echo "  • solax_data2: $SOLAX_DATA2_COUNT εγγραφές"

# Έλεγχος αν τα δεδομένα είναι λογικά
if [ "$TABLES_COUNT" -gt 90 ] && [ "$RECORDS_COUNT" -gt 1000000 ]; then
    echo -e "${GREEN}✅ Η επαναφορά φαίνεται επιτυχής${NC}"
else
    echo -e "${YELLOW}⚠️  Προειδοποίηση: Τα δεδομένα μπορεί να είναι ελλιπή${NC}"
fi

# Τελικό μήνυμα
echo ""
echo -e "${GREEN}🎉 Επαναφορά βάσης δεδομένων ολοκληρώθηκε!${NC}"
echo ""
echo "💡 Επόμενα βήματα:"
echo "  • Ελέγξτε τη λειτουργία της εφαρμογής"
echo "  • Επαληθεύστε τα δεδομένα"
echo "  • Εκτελέστε VACUUM ANALYZE για βελτιστοποίηση"
echo ""
echo "🔧 Χρήσιμες εντολές:"
echo "  • Έλεγχος σύνδεσης: pg_isready -h $DB_HOST -p $DB_PORT"
echo "  • Σύνδεση στη βάση: psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
echo "  • Βελτιστοποίηση: psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c 'VACUUM ANALYZE;'"
