{"id": "c790ccc58b40ba2b52f6de06730f1c61be0e3f077e346d5084d01d5bda261d9d", "parent": "ea37df4a46e078c05c569a82e02a54eb48763fa09da735c5ab81ddd8706ae1aa", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}