@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Solar Prediction System - Fixed Installation Script
:: This version handles multiple Docker image formats

echo.
echo Solar Prediction System - Fixed Installation
echo ===========================================
echo.

:: Check Docker
echo Checking Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not running
    echo.
    echo Please:
    echo 1. Install Docker Desktop from: https://docs.docker.com/desktop/windows/
    echo 2. Make sure Docker Desktop is running
    echo 3. Restart Command Prompt
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Docker found

:: Check Docker daemon
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker daemon is not running
    echo Please start Docker Desktop
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Docker daemon is running

:: Find available image file
set IMAGE_FILE=
if exist "solar-prediction-complete.tar.gz" (
    set IMAGE_FILE=solar-prediction-complete.tar.gz
    echo Found: solar-prediction-complete.tar.gz
) else if exist "solar-prediction-complete-package.tar.gz" (
    set IMAGE_FILE=solar-prediction-complete-package.tar.gz
    echo Found: solar-prediction-complete-package.tar.gz
) else (
    echo ERROR: No Docker image file found
    echo Looking for:
    echo - solar-prediction-complete.tar.gz
    echo - solar-prediction-complete-package.tar.gz
    echo.
    echo Available files:
    dir *.tar.gz 2>nul
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Using image file: %IMAGE_FILE%

:: Check if we have OCI format files (manifest.json, oci-layout, blobs/)
if exist "manifest.json" if exist "oci-layout" if exist "blobs" (
    echo.
    echo Detected OCI format container image
    echo This appears to be a container registry export
    echo.
    echo Attempting to load OCI format...
    
    :: Try to load the current directory as OCI format
    docker load -i "%IMAGE_FILE%" 2>nul
    if not errorlevel 1 (
        echo SUCCESS: OCI image loaded
        goto :check_image
    )
    
    echo OCI load failed, trying alternative methods...
)

:: Method 1: Direct tar.gz load
echo.
echo Method 1: Attempting direct Docker load...
docker load -i "%IMAGE_FILE%" 2>nul
if not errorlevel 1 (
    echo SUCCESS: Direct load completed
    goto :check_image
)

:: Method 2: Extract and load
echo Method 2: Extracting and loading...

:: Check for tar command
where tar >nul 2>&1
if errorlevel 1 (
    echo ERROR: tar command not found
    echo This script requires Windows 10 version 1903 or later
    echo Or install 7-Zip and use install_solar_docker.bat
    pause
    exit /b 1
)

:: Extract the archive
echo Extracting %IMAGE_FILE%...
tar -xzf "%IMAGE_FILE%" 2>nul
if errorlevel 1 (
    echo ERROR: Failed to extract %IMAGE_FILE%
    echo The file may be corrupted or in an unsupported format
    pause
    exit /b 1
)

:: Look for extracted tar files
set TAR_FILE=
if exist "solar-prediction-complete.tar" (
    set TAR_FILE=solar-prediction-complete.tar
) else if exist "solar-prediction-complete-package.tar" (
    set TAR_FILE=solar-prediction-complete-package.tar
) else (
    echo Searching for any .tar files...
    for %%f in (*.tar) do (
        set TAR_FILE=%%f
        goto :found_tar
    )
    echo ERROR: No .tar file found after extraction
    pause
    exit /b 1
)

:found_tar
echo Found extracted file: %TAR_FILE%
echo Loading Docker image...
docker load -i "%TAR_FILE%"
if errorlevel 1 (
    echo ERROR: Failed to load Docker image from %TAR_FILE%
    pause
    exit /b 1
)

:: Cleanup
echo Cleaning up extracted files...
del "%TAR_FILE%" >nul 2>&1

:check_image
echo SUCCESS: Docker image loaded

:: List loaded images to verify
echo.
echo Checking loaded images...
docker images | findstr solar-prediction
if errorlevel 1 (
    echo WARNING: No solar-prediction image found in Docker images
    echo Available images:
    docker images
    echo.
    echo You may need to tag the image manually
    pause
)

:: Check if container exists and remove if needed
docker ps -a --filter "name=solar-prediction-system" --format "{{.Names}}" | findstr "solar-prediction-system" >nul
if not errorlevel 1 (
    echo.
    echo WARNING: Container 'solar-prediction-system' already exists
    set /p "REMOVE=Remove existing container? (y/N): "
    if /i "!REMOVE!"=="y" (
        echo Removing existing container...
        docker stop solar-prediction-system >nul 2>&1
        docker rm solar-prediction-system >nul 2>&1
    ) else (
        echo Installation cancelled
        pause
        exit /b 1
    )
)

:: Try to find the correct image name
set DOCKER_IMAGE=
for /f "tokens=1" %%i in ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr solar') do (
    set DOCKER_IMAGE=%%i
    goto :found_image
)

:: If no solar image found, try common names
if "%DOCKER_IMAGE%"=="" (
    docker images --format "{{.Repository}}:{{.Tag}}" | findstr "prediction" >nul
    if not errorlevel 1 (
        for /f "tokens=1" %%i in ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr prediction') do (
            set DOCKER_IMAGE=%%i
            goto :found_image
        )
    )
)

if "%DOCKER_IMAGE%"=="" (
    echo ERROR: Could not determine Docker image name
    echo Available images:
    docker images
    echo.
    echo Please run manually:
    echo docker run -d --name solar-prediction-system --restart unless-stopped -p 5432:5432 -p 8100:8100 -p 8109:8109 -p 8110:8110 [IMAGE_NAME]
    pause
    exit /b 1
)

:found_image
echo Using Docker image: %DOCKER_IMAGE%

:: Start container
echo.
echo Starting Solar Prediction System...
docker run -d ^
    --name solar-prediction-system ^
    --restart unless-stopped ^
    -p 5432:5432 ^
    -p 8100:8100 ^
    -p 8109:8109 ^
    -p 8110:8110 ^
    "%DOCKER_IMAGE%"

if errorlevel 1 (
    echo ERROR: Failed to start container
    echo.
    echo Container logs:
    docker logs solar-prediction-system 2>nul
    pause
    exit /b 1
)

:: Wait and check
echo Waiting for services to start (30 seconds)...
timeout /t 30 /nobreak >nul

docker ps --filter "name=solar-prediction-system" --format "{{.Status}}" | findstr /i "Up" >nul
if errorlevel 1 (
    echo ERROR: Container failed to start properly
    echo.
    echo Container logs:
    docker logs solar-prediction-system
    echo.
    echo Troubleshooting:
    echo - Check if ports 5432, 8100, 8109, 8110 are free
    echo - Ensure you have enough memory (4GB+)
    echo - Try: docker restart solar-prediction-system
    pause
    exit /b 1
)

:: Success
echo.
echo SUCCESS: Solar Prediction System is running!
echo.
echo Available services:
echo - Web Interface: http://localhost:8100
echo - API Documentation: http://localhost:8100/docs
echo - PostgreSQL: localhost:5432 (postgres/postgres)
echo - Telegram Bot: Port 8109
echo - Enhanced Billing: Port 8110
echo.
echo Management commands:
echo - Check status: docker ps
echo - View logs: docker logs solar-prediction-system
echo - Stop system: docker stop solar-prediction-system
echo - Start system: docker start solar-prediction-system
echo - Remove system: docker rm -f solar-prediction-system
echo.

set /p "OPEN_WEB=Open web interface now? (y/N): "
if /i "!OPEN_WEB!"=="y" (
    start http://localhost:8100
)

echo.
echo Installation completed!
pause
