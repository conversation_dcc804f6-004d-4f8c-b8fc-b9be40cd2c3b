#!/bin/bash

# Solar Prediction System - Docker Installation Script
# Χρήση: ./install_solar_docker.sh

set -e

echo "🚀 Solar Prediction System - Docker Installation"
echo "================================================"

# Έλεγχος αν υπάρχει το Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Το Docker δεν είναι εγκατεστημένο"
    echo "Παρακαλώ εγκαταστήστε το Docker πρώτα: https://docs.docker.com/get-docker/"
    exit 1
fi

echo "✅ Docker βρέθηκε"

# Έλεγχος αν υπάρχει το αρχείο image
IMAGE_FILE="solar-prediction-complete.tar.gz"
if [ ! -f "$IMAGE_FILE" ]; then
    echo "❌ Το αρχείο $IMAGE_FILE δεν βρέθηκε"
    echo "Παρακαλώ βεβαιωθείτε ότι το αρχείο βρίσκεται στον ίδιο φάκελο"
    exit 1
fi

echo "✅ Αρχείο image βρέθηκε: $IMAGE_FILE"

# Αποσυμπίεση
echo "📦 Αποσυμπίεση αρχείου..."
gunzip -f "$IMAGE_FILE"

# Φόρτωση στο Docker
echo "🐳 Φόρτωση Docker image..."
docker load -i solar-prediction-complete.tar

# Καθαρισμός
echo "🧹 Καθαρισμός προσωρινών αρχείων..."
rm -f solar-prediction-complete.tar

# Έλεγχος αν το container τρέχει ήδη
if docker ps -a | grep -q solar-prediction-system; then
    echo "⚠️  Το container 'solar-prediction-system' υπάρχει ήδη"
    read -p "Θέλετε να το αφαιρέσετε και να δημιουργήσετε νέο; (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  Αφαίρεση παλιού container..."
        docker stop solar-prediction-system 2>/dev/null || true
        docker rm solar-prediction-system 2>/dev/null || true
    else
        echo "❌ Εγκατάσταση ακυρώθηκε"
        exit 1
    fi
fi

# Εκκίνηση container
echo "🚀 Εκκίνηση Solar Prediction System..."
docker run -d \
  --name solar-prediction-system \
  --restart unless-stopped \
  -p 5432:5432 \
  -p 8100:8100 \
  -p 8109:8109 \
  -p 8110:8110 \
  solar-prediction-complete:latest

# Αναμονή για εκκίνηση
echo "⏳ Αναμονή για εκκίνηση υπηρεσιών..."
sleep 30

# Έλεγχος κατάστασης
if docker ps | grep -q solar-prediction-system; then
    echo "✅ Το Solar Prediction System ξεκίνησε επιτυχώς!"
    echo ""
    echo "🌐 Διαθέσιμες υπηρεσίες:"
    echo "   • Web Interface: http://localhost:8100"
    echo "   • API Documentation: http://localhost:8100/docs"
    echo "   • PostgreSQL: localhost:5432 (postgres/postgres)"
    echo "   • Telegram Bot: Port 8109"
    echo "   • Enhanced Billing: Port 8110"
    echo ""
    echo "📊 Για έλεγχο κατάστασης:"
    echo "   docker logs solar-prediction-system"
    echo ""
    echo "🛑 Για διακοπή:"
    echo "   docker stop solar-prediction-system"
    echo ""
    echo "🔄 Για επανεκκίνηση:"
    echo "   docker start solar-prediction-system"
else
    echo "❌ Πρόβλημα κατά την εκκίνηση"
    echo "📋 Logs:"
    docker logs solar-prediction-system
    exit 1
fi

echo ""
echo "🎉 Εγκατάσταση ολοκληρώθηκε επιτυχώς!"
