{"id": "f79ca638f1646437dd67aed87d460e2731c0f55ecfa214a789ceb6a6127fc8a5", "parent": "b7cdbb27ecfdb247649e0ddf070a0327febb2479f1f1a600cb3704f5f910d52e", "created": "2025-06-28T20:03:21.38770158Z", "container": "906dae7ded02d713bb461ca3c2a44eabbd61210f23e457e8748fa2ca7b27d37a", "container_config": {"Hostname": "906dae7ded02", "Domainname": "", "User": "root", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "ExposedPorts": {"5432/tcp": {}, "8100/tcp": {}, "8109/tcp": {}, "8110/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "LANG=C.UTF-8", "GPG_KEY=A035C8C19219BA821ECEA86B64E628F8D684696D", "PYTHON_VERSION=3.11.13", "PYTHON_SHA256=8fb5f9fbc7609fa822cb31549884575db7fd9657cbffb89510b5d7975963a83a", "DEBIAN_FRONTEND=noninteractive", "PYTHONUNBUFFERED=1", "PYTHONDONTWRITEBYTECODE=1", "PYTHONPATH=/app:/app/src", "PYTHONRECURSIONLIMIT=10000", "TZ=Europe/Athens", "PGDATA=/var/lib/postgresql/data", "POSTGRES_DB=solar_prediction", "POSTGRES_USER=postgres", "POSTGRES_PASSWORD=postgres"], "Cmd": ["/bin/sh", "-c", "#(nop) ", "CMD [\"/app/start_complete_system.sh\"]"], "Healthcheck": {"Test": ["CMD-SHELL", "curl -f http://localhost:8100/health || exit 1"], "Interval": 30000000000, "Timeout": 10000000000, "StartPeriod": 60000000000, "Retries": 3}, "Image": "sha256:c9a93b7b029cc70596e9eedef6d322114888b68b0ebc6b0aacc8e8d2fedd3e72", "Volumes": null, "WorkingDir": "/app", "Entrypoint": null, "OnBuild": null, "Labels": {}}, "docker_version": "27.5.1", "config": {"Hostname": "", "Domainname": "", "User": "root", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "ExposedPorts": {"5432/tcp": {}, "8100/tcp": {}, "8109/tcp": {}, "8110/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "LANG=C.UTF-8", "GPG_KEY=A035C8C19219BA821ECEA86B64E628F8D684696D", "PYTHON_VERSION=3.11.13", "PYTHON_SHA256=8fb5f9fbc7609fa822cb31549884575db7fd9657cbffb89510b5d7975963a83a", "DEBIAN_FRONTEND=noninteractive", "PYTHONUNBUFFERED=1", "PYTHONDONTWRITEBYTECODE=1", "PYTHONPATH=/app:/app/src", "PYTHONRECURSIONLIMIT=10000", "TZ=Europe/Athens", "PGDATA=/var/lib/postgresql/data", "POSTGRES_DB=solar_prediction", "POSTGRES_USER=postgres", "POSTGRES_PASSWORD=postgres"], "Cmd": ["/app/start_complete_system.sh"], "Healthcheck": {"Test": ["CMD-SHELL", "curl -f http://localhost:8100/health || exit 1"], "Interval": 30000000000, "Timeout": 10000000000, "StartPeriod": 60000000000, "Retries": 3}, "Image": "sha256:c9a93b7b029cc70596e9eedef6d322114888b68b0ebc6b0aacc8e8d2fedd3e72", "Volumes": null, "WorkingDir": "/app", "Entrypoint": null, "OnBuild": null, "Labels": null}, "architecture": "amd64", "os": "linux"}