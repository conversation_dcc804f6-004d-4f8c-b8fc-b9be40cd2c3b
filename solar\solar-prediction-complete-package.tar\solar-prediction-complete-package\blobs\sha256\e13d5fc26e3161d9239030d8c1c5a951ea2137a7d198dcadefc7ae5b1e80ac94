{"id": "2c80f5404d97505c78b77552794dde369f54f16ec937a3e6959918ddea78a869", "parent": "3b2a59212e56517d918bc5999ce7c31a59ece83e3fbf24ead96c3ff5c31a5b9e", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}