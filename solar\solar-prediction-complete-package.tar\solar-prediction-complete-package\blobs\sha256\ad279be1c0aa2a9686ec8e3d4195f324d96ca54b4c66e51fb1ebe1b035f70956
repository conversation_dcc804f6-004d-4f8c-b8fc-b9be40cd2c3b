{"id": "afa2645003f53582cbdc24f0fa3721738027bf1595c46c4fe41bac0f179fbb5a", "parent": "cfefec3e0fd7d2a458e5cd61b8d8159ef54f1950619a8bef170e1092c447f216", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}