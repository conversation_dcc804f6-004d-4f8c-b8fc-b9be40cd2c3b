{"id": "70a43724f51fd110ad55d9a8b953096296f1c07bc9eaa9b4d88546732feefd3a", "parent": "afa2645003f53582cbdc24f0fa3721738027bf1595c46c4fe41bac0f179fbb5a", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}