{"id": "528b6e1c91813f436b99a699b867b78c071088a01e5254f7daeae302c3fcfb82", "parent": "0f4f766cc962588ef3f713e8604733e66a4b278fddd539071918e76648695828", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}