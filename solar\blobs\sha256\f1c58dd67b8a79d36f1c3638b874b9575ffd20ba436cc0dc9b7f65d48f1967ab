{"id": "3b2a59212e56517d918bc5999ce7c31a59ece83e3fbf24ead96c3ff5c31a5b9e", "parent": "152281c1e71d0d62264d195d0810b92ee7520b78bc2850570d48dc14804d2d1e", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}