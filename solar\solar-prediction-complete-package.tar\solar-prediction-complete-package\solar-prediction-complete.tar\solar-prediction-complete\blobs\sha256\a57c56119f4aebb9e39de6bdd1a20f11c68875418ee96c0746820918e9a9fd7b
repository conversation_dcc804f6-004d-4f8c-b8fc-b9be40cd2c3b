{"id": "152281c1e71d0d62264d195d0810b92ee7520b78bc2850570d48dc14804d2d1e", "parent": "74821328a66b622bb7c01bccec70d2d63509ea6abab91fd38e165672e3441f7c", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}