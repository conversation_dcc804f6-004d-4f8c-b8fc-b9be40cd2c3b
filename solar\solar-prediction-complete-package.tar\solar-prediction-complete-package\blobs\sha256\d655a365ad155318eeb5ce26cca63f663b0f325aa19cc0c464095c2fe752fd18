{"id": "cfefec3e0fd7d2a458e5cd61b8d8159ef54f1950619a8bef170e1092c447f216", "parent": "d0a438c1128206b89e259be7bc9fa13094cf31d05f7d23bd6a4e5ab8c13dd177", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}