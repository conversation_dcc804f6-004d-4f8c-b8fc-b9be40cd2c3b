{"id": "d0a438c1128206b89e259be7bc9fa13094cf31d05f7d23bd6a4e5ab8c13dd177", "parent": "5e17ae590e56bf64e566911a8ddefb32778ade52496254a7eb591790fe9e7bd1", "created": "1970-01-01T02:00:00+02:00", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "os": "linux"}